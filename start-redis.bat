@echo off
echo ========================================
echo 启动Redis服务 (带密码认证)
echo ========================================

echo.
echo 检查Redis是否已安装...

where redis-server >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未找到redis-server命令
    echo 请先安装Redis或将Redis添加到系统PATH
    echo.
    echo 下载地址: https://github.com/microsoftarchive/redis/releases
    echo 或使用Chocolatey: choco install redis-64
    echo.
    pause
    exit /b 1
)

echo Redis已安装 ✓
echo.

echo 正在启动Redis服务...
echo 配置文件: redis.conf
echo 密码认证: 已启用
echo.

:: 启动Redis服务
redis-server redis.conf

echo.
echo Redis服务已停止
pause 