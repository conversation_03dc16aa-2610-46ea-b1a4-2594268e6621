{"nav": {"home": "首页", "tools": "AI工具", "pricing": "定价", "docs": "文档", "blog": "博客", "login": "登录", "register": "注册", "logout": "退出", "profile": "个人中心", "credits": "积分", "apiKeys": "API密钥"}, "hero": {"title": "AI工具集合平台", "subtitle": "智能化您的工作流程", "getStarted": "立即开始", "learnMore": "了解更多", "description": "集成多种强大的AI工具，提供文本处理、语言分析、图像生成等功能。简单易用的API接口，让您的应用瞬间拥有AI能力。", "featuresTitle": "强大的AI工具集合", "featuresSubtitle": "我们提供多种经过优化的AI工具，帮助您提高工作效率", "ctaTitle": "准备好体验AI的强大力量了吗？", "ctaSubtitle": "立即开始使用我们的AI工具，提升您的工作效率", "tryFree": "免费试用"}, "auth": {"loginTitle": "登录您的账户", "registerTitle": "创建新账户", "email": "邮箱", "emailPlaceholder": "请输入您的邮箱", "username": "用户名", "usernamePlaceholder": "请输入用户名", "fullName": "姓名", "fullNamePlaceholder": "请输入您的姓名", "verificationCode": "验证码", "verificationCodePlaceholder": "请输入验证码", "sendCode": "发送验证码", "resendCode": "重新发送", "loginButton": "登录", "registerButton": "注册", "or": "或", "googleLogin": "使用Google登录", "googleRegister": "使用Google注册", "switchToRegister": "没有账户？立即注册", "switchToLogin": "已有账户？立即登录"}, "tools": {"title": "AI工具集合", "subtitle": "探索我们强大的AI工具集合，每个工具都经过精心优化，为您提供最佳的使用体验", "categories": "分类", "allCategories": "全部工具", "search": "搜索工具...", "viewDetails": "查看详情", "tryTool": "立即使用", "creditsRequired": "消耗积分", "inputParameters": "请求参数", "outputFormat": "输出格式", "example": "示例", "apiEndpoint": "API端点", "method": "请求方法", "rateLimit": "速率限制", "noToolsFound": "没有找到相关工具", "tryOtherCategories": "请尝试选择其他分类或稍后再试", "toolNotFound": "工具不存在", "checkUrl": "请检查URL是否正确", "apiDebugger": "API调试窗口", "callApi": "调用API", "calling": "调用中...", "waitSeconds": "请等待 {seconds} 秒", "rateLimitNotice": "为了防止滥用，未登录用户每分钟只能调用1次", "callResult": "调用结果", "clickToTest": "点击左侧「调用API」按钮查看结果", "requestExample": "请求示例", "responseExample": "响应示例", "executionTime": "执行时间", "error": "错误", "generatedResult": "生成结果", "prompt": "提示词", "style": "风格", "size": "尺寸"}, "pricing": {"title": "选择适合您的方案", "subtitle": "灵活的定价选项，满足不同需求", "monthly": "月付", "yearly": "年付", "savePercent": "节省 {percent}%", "free": "免费", "pro": "专业版", "enterprise": "企业版", "perMonth": "/月", "perYear": "/年", "features": "功能特性", "getStarted": "开始使用", "contactSales": "联系销售", "popularPlan": "热门方案", "freeFeatures": {"feature1": "每日 10 次免费调用", "feature2": "基础工具访问", "feature3": "社区支持", "feature4": "基础文档"}, "proFeatures": {"feature1": "每月 1000 次调用", "feature2": "所有高级工具", "feature3": "优先支持", "feature4": "API 访问", "feature5": "自定义集成"}, "enterpriseFeatures": {"feature1": "无限调用", "feature2": "专属客户经理", "feature3": "SLA 保证", "feature4": "本地部署选项", "feature5": "定制开发"}}, "blog": {"title": "博客", "subtitle": "最新的AI技术资讯和使用技巧", "readMore": "阅读更多", "publishedOn": "发布于", "author": "作者", "tags": "标签", "relatedPosts": "相关文章", "sharePost": "分享文章", "comments": "评论", "leaveComment": "发表评论", "noPostsFound": "没有找到文章", "backToBlog": "返回博客"}, "apiKeys": {"title": "API Keys 管理", "subtitle": "管理您的API Keys，用于程序化访问我们的服务", "createKey": "创建 API Key", "usage": "使用说明", "usageInstructions": {"1": "使用API Key可以绕过频率限制，但会消耗您的积分", "2": "请在请求头中添加 Authorization: Bearer your-api-key", "3": "每个用户最多可以创建5个API Key"}, "currentBalance": "当前余额: {credits} 积分", "keyCreated": "API Key 创建成功", "saveKeyNotice": "请立即保存您的API Key，出于安全考虑，我们不会再次显示：", "copy": "复制", "close": "关闭", "noKeysFound": "您还没有创建任何API Key", "usageCount": "使用次数", "createdAt": "创建时间", "lastUsed": "最后使用", "delete": "删除", "createNewKey": "创建新的 API Key", "keyName": "名称", "keyNamePlaceholder": "例如：生产环境、测试用途", "keyDescription": "描述（可选）", "keyDescriptionPlaceholder": "简要描述这个API Key的用途", "create": "创建", "cancel": "取消", "required": "必填", "deleteConfirm": "确定要删除这个API Key吗？"}, "footer": {"company": "AI工具平台", "description": "提供最优质的AI工具服务", "links": "快速链接", "support": "支持", "legal": "法律条款", "privacyPolicy": "隐私政策", "termsOfService": "服务条款"}, "common": {"loading": "加载中...", "error": "出错了", "retry": "重试", "save": "保存", "cancel": "取消", "confirm": "确认", "success": "成功", "failed": "失败", "submit": "提交", "close": "关闭", "back": "返回", "next": "下一步", "previous": "上一步", "language": "语言", "yes": "是", "no": "否", "optional": "可选", "required": "必填"}, "jsonParser": {"title": "JSON解析器", "description": "解析和美化JSON格式文本，提供格式化和错误检查功能", "inputLabel": "输入JSON文本", "inputPlaceholder": "请输入需要解析的JSON文本...", "formatType": "格式化类型", "formatTypes": {"pretty": "美化格式", "compact": "紧凑格式", "validate": "仅验证"}, "indent": "缩进空格数", "parseButton": "解析JSON", "result": "解析结果", "isValid": "JSON有效性", "valid": "有效", "invalid": "无效", "errorMessage": "错误信息", "originalText": "原始文本", "parsedJson": "解析后的JSON"}}