{"nav": {"home": "ホーム", "tools": "AIツール", "pricing": "価格", "docs": "ドキュメント", "login": "ログイン", "register": "登録", "logout": "ログアウト", "profile": "プロフィール", "credits": "クレジット"}, "hero": {"title": "強力なAIツールコレクション", "subtitle": "生産性を向上させるワンストップAIソリューション", "getStarted": "始めましょう", "learnMore": "詳細を見る"}, "auth": {"loginTitle": "アカウントにログイン", "registerTitle": "新しいアカウントを作成", "email": "メールアドレス", "emailPlaceholder": "メールアドレスを入力", "username": "ユーザー名", "usernamePlaceholder": "ユーザー名を入力", "fullName": "氏名", "fullNamePlaceholder": "氏名を入力", "verificationCode": "認証コード", "verificationCodePlaceholder": "認証コードを入力", "sendCode": "コード送信", "resendCode": "再送信", "loginButton": "ログイン", "registerButton": "登録", "or": "または", "googleLogin": "Googleでログイン", "googleRegister": "Googleで登録", "switchToRegister": "アカウントをお持ちでない方は登録", "switchToLogin": "すでにアカウントをお持ちの方はログイン"}, "tools": {"title": "AIツール", "subtitle": "生産性を向上させる適切なツールを選択してください", "categories": "カテゴリ", "allCategories": "すべてのカテゴリ", "search": "ツールを検索...", "viewDetails": "詳細を見る", "tryTool": "ツールを試す", "creditsRequired": "必要クレジット", "inputParameters": "入力パラメータ", "outputFormat": "出力形式", "example": "例", "apiEndpoint": "APIエンドポイント", "method": "リクエストメソッド", "rateLimit": "レート制限"}, "footer": {"company": "AIツールプラットフォーム", "description": "最高のAIツールサービスを提供", "links": "クイックリンク", "support": "サポート", "legal": "法的事項", "privacyPolicy": "プライバシーポリシー", "termsOfService": "利用規約"}, "common": {"loading": "読み込み中...", "error": "エラー", "retry": "再試行", "save": "保存", "cancel": "キャンセル", "confirm": "確認", "success": "成功", "failed": "失敗", "submit": "送信", "close": "閉じる", "back": "戻る", "next": "次へ", "previous": "前へ", "language": "言語"}, "jsonParser": {"title": "JSONパーサー", "description": "JSON形式のテキストを解析・整形し、エラーチェック機能を提供", "inputLabel": "JSONテキストを入力", "inputPlaceholder": "解析するJSONテキストを入力してください...", "formatType": "フォーマットタイプ", "formatTypes": {"pretty": "プリティフォーマット", "compact": "コンパクトフォーマット", "validate": "検証のみ"}, "indent": "インデントスペース数", "parseButton": "JSON解析", "result": "解析結果", "isValid": "JSON有効性", "valid": "有効", "invalid": "無効", "errorMessage": "エラーメッセージ", "originalText": "元のテキスト", "parsedJson": "解析されたJSON"}}