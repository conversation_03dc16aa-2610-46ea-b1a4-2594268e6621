{"nav": {"home": "홈", "tools": "AI 도구", "pricing": "가격", "docs": "문서", "login": "로그인", "register": "회원가입", "logout": "로그아웃", "profile": "프로필", "credits": "크레딧"}, "hero": {"title": "강력한 AI 도구 컬렉션", "subtitle": "생산성을 높이는 원스톱 AI 솔루션", "getStarted": "시작하기", "learnMore": "자세히 보기"}, "auth": {"loginTitle": "계정에 로그인", "registerTitle": "새 계정 생성", "email": "이메일", "emailPlaceholder": "이메일을 입력하세요", "username": "사용자명", "usernamePlaceholder": "사용자명을 입력하세요", "fullName": "이름", "fullNamePlaceholder": "이름을 입력하세요", "verificationCode": "인증 코드", "verificationCodePlaceholder": "인증 코드를 입력하세요", "sendCode": "코드 전송", "resendCode": "재전송", "loginButton": "로그인", "registerButton": "회원가입", "or": "또는", "googleLogin": "Google로 로그인", "googleRegister": "Google로 회원가입", "switchToRegister": "계정이 없으신가요? 지금 가입하세요", "switchToLogin": "이미 계정이 있으신가요? 지금 로그인하세요"}, "tools": {"title": "AI 도구", "subtitle": "생산성을 높일 수 있는 적합한 도구를 선택하세요", "categories": "카테고리", "allCategories": "모든 카테고리", "search": "도구 검색...", "viewDetails": "자세히 보기", "tryTool": "도구 사용해보기", "creditsRequired": "필요 크레딧", "inputParameters": "입력 매개변수", "outputFormat": "출력 형식", "example": "예시", "apiEndpoint": "API 엔드포인트", "method": "요청 방법", "rateLimit": "요청 제한"}, "footer": {"company": "AI 도구 플랫폼", "description": "최고의 AI 도구 서비스 제공", "links": "빠른 링크", "support": "지원", "legal": "법적 고지", "privacyPolicy": "개인정보처리방침", "termsOfService": "이용약관"}, "common": {"loading": "로딩 중...", "error": "오류", "retry": "다시 시도", "save": "저장", "cancel": "취소", "confirm": "확인", "success": "성공", "failed": "실패", "submit": "제출", "close": "닫기", "back": "뒤로", "next": "다음", "previous": "이전", "language": "언어"}, "jsonParser": {"title": "JSON 파서", "description": "JSON 형식 텍스트를 파싱하고 정리하며 오류 확인 기능을 제공합니다", "inputLabel": "JSON 텍스트 입력", "inputPlaceholder": "파싱할 JSON 텍스트를 입력하세요...", "formatType": "형식 유형", "formatTypes": {"pretty": "예쁜 형식", "compact": "압축 형식", "validate": "검증만"}, "indent": "들여쓰기 공백 수", "parseButton": "JSON 파싱", "result": "파싱 결과", "isValid": "JSON 유효성", "valid": "유효", "invalid": "무효", "errorMessage": "오류 메시지", "originalText": "원본 텍스트", "parsedJson": "파싱된 JSON"}}