{"nav": {"home": "Home", "tools": "AI Tools", "pricing": "Pricing", "docs": "Docs", "blog": "Blog", "login": "<PERSON><PERSON>", "register": "Register", "logout": "Logout", "profile": "Profile", "credits": "Credits", "apiKeys": "API Keys"}, "hero": {"title": "AI Tools Platform", "subtitle": "Streamline Your Workflow with Intelligence", "getStarted": "Get Started", "learnMore": "Learn More", "description": "Integrate powerful AI tools with text processing, language analysis, and image generation capabilities. Easy-to-use APIs to instantly empower your applications with AI.", "featuresTitle": "Powerful AI Tool Collection", "featuresSubtitle": "We provide various optimized AI tools to help you improve your productivity", "ctaTitle": "Ready to Experience the Power of AI?", "ctaSubtitle": "Start using our AI tools now to enhance your productivity", "tryFree": "Try for Free"}, "auth": {"loginTitle": "Login to Your Account", "registerTitle": "Create New Account", "email": "Email", "emailPlaceholder": "Enter your email", "username": "Username", "usernamePlaceholder": "Enter username", "fullName": "Full Name", "fullNamePlaceholder": "Enter your full name", "verificationCode": "Verification Code", "verificationCodePlaceholder": "Enter verification code", "sendCode": "Send Code", "resendCode": "Resend", "loginButton": "<PERSON><PERSON>", "registerButton": "Register", "or": "or", "googleLogin": "Login with Google", "googleRegister": "Register with Google", "switchToRegister": "Don't have an account? Register now", "switchToLogin": "Already have an account? Login now"}, "tools": {"title": "AI Tool Collection", "subtitle": "Explore our powerful AI tool collection, each tool carefully optimized for the best user experience", "categories": "Categories", "allCategories": "All Tools", "search": "Search tools...", "viewDetails": "View Details", "tryTool": "Try Now", "creditsRequired": "Credits Required", "inputParameters": "Request Parameters", "outputFormat": "Output Format", "example": "Example", "apiEndpoint": "API Endpoint", "method": "Request Method", "rateLimit": "Rate Limit", "noToolsFound": "No tools found", "tryOtherCategories": "Try selecting other categories or try again later", "toolNotFound": "Tool not found", "checkUrl": "Please check if the URL is correct", "apiDebugger": "API Debugger", "callApi": "Call API", "calling": "Calling...", "waitSeconds": "Please wait {seconds} seconds", "rateLimitNotice": "To prevent abuse, unauthenticated users can only call once per minute", "callResult": "Call Result", "clickToTest": "Click the 'Call API' button on the left to see results", "requestExample": "Request Example", "responseExample": "Response Example", "executionTime": "Execution Time", "error": "Error", "generatedResult": "Generated Result", "prompt": "Prompt", "style": "Style", "size": "Size"}, "pricing": {"title": "Choose the Right Plan for You", "subtitle": "Flexible pricing options to meet different needs", "monthly": "Monthly", "yearly": "Yearly", "savePercent": "Save {percent}%", "free": "Free", "pro": "Professional", "enterprise": "Enterprise", "perMonth": "/month", "perYear": "/year", "features": "Features", "getStarted": "Get Started", "contactSales": "Contact Sales", "popularPlan": "Popular Plan", "freeFeatures": {"feature1": "10 free calls per day", "feature2": "Basic tool access", "feature3": "Community support", "feature4": "Basic documentation"}, "proFeatures": {"feature1": "1000 calls per month", "feature2": "All premium tools", "feature3": "Priority support", "feature4": "API access", "feature5": "Custom integrations"}, "enterpriseFeatures": {"feature1": "Unlimited calls", "feature2": "Dedicated account manager", "feature3": "SLA guarantee", "feature4": "On-premise deployment", "feature5": "Custom development"}}, "blog": {"title": "Blog", "subtitle": "Latest AI technology news and usage tips", "readMore": "Read More", "publishedOn": "Published on", "author": "Author", "tags": "Tags", "relatedPosts": "Related Posts", "sharePost": "Share Post", "comments": "Comments", "leaveComment": "Leave a Comment", "noPostsFound": "No posts found", "backToBlog": "Back to Blog"}, "apiKeys": {"title": "API Keys Management", "subtitle": "Manage your API Keys for programmatic access to our services", "createKey": "Create API Key", "usage": "Usage Instructions", "usageInstructions": {"1": "Using API Keys bypasses rate limits but consumes your credits", "2": "Add Authorization: Bearer your-api-key to request headers", "3": "Each user can create up to 5 API Keys"}, "currentBalance": "Current balance: {credits} credits", "keyCreated": "API Key Created Successfully", "saveKeyNotice": "Please save your API Key immediately. For security reasons, we will not display it again:", "copy": "Copy", "close": "Close", "noKeysFound": "You haven't created any API Keys yet", "usageCount": "Usage Count", "createdAt": "Created At", "lastUsed": "Last Used", "delete": "Delete", "createNewKey": "Create New API Key", "keyName": "Name", "keyNamePlaceholder": "e.g. Production, Testing", "keyDescription": "Description (Optional)", "keyDescriptionPlaceholder": "Brief description of this API Key's purpose", "create": "Create", "cancel": "Cancel", "required": "Required", "deleteConfirm": "Are you sure you want to delete this API Key?"}, "footer": {"company": "AI Tools Platform", "description": "Providing the best AI tool services", "links": "Quick Links", "support": "Support", "legal": "Legal", "privacyPolicy": "Privacy Policy", "termsOfService": "Terms of Service"}, "common": {"loading": "Loading...", "error": "Error", "retry": "Retry", "save": "Save", "cancel": "Cancel", "confirm": "Confirm", "success": "Success", "failed": "Failed", "submit": "Submit", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "language": "Language", "yes": "Yes", "no": "No", "optional": "Optional", "required": "Required"}, "jsonParser": {"title": "JSON Parser", "description": "Parse and beautify JSON text with formatting and error checking", "inputLabel": "Input JSON Text", "inputPlaceholder": "Enter JSON text to parse...", "formatType": "Format Type", "formatTypes": {"pretty": "Pretty Format", "compact": "Compact Format", "validate": "Validate Only"}, "indent": "Indent Spaces", "parseButton": "Parse JSON", "result": "<PERSON><PERSON>t", "isValid": "JSON Validity", "valid": "<PERSON><PERSON>", "invalid": "Invalid", "errorMessage": "Error Message", "originalText": "Original Text", "parsedJson": "Parsed JSON"}}