export interface APITool {
  id: number;
  name: string;
  description: string;
  category: string;
  icon: string;
  endpoint: string;
  method: string;
  input_schema: any;
  output_schema: any;
  is_active: boolean;
  created_at: string;
  updated_at?: string;
  total_calls: number;
  success_rate: number;
  cost_per_request: number;
}

export interface APIToolCategory {
  name: string;
  count: number;
  icon: string;
}

export interface APICallRequest {
  tool_id: number;
  parameters: Record<string, any>;
}

export interface APICallResponse {
  success: boolean;
  data?: Record<string, any>;
  error?: string;
  credits_used: number;
  execution_time: number;
}

export interface TextBeautifyRequest {
  text: string;
  style?: 'professional' | 'casual' | 'formal' | 'creative';
}

export interface LanguageDetectRequest {
  text: string;
}

export interface TextToImageRequest {
  text: string;
  style?: 'realistic' | 'cartoon' | 'artistic' | 'abstract';
  size?: '1024x1024' | '512x512' | '256x256';
}

export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp?: string;
}

export interface PaginatedResponse<T> {
  tools: T[];
  total: number;
  page: number;
  limit: number;
} 