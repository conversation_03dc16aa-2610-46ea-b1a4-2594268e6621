export interface User {
  id: number;
  email: string;
  username: string;
  full_name?: string;
  avatar_url?: string;
  credits: number;
  is_verified: boolean;
  created_at: string;
}

export interface AuthResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
  user: User;
}

export interface LoginRequest {
  email: string;
  verification_code: string;
}

export interface RegisterRequest {
  email: string;
  username: string;
  full_name?: string;
  verification_code: string;
}

export interface SendCodeRequest {
  email: string;
  type: 'login' | 'register';
}

export interface GoogleOAuthResponse {
  credential: string;
  select_by: string;
}

export interface VerificationCodeResponse {
  success: boolean;
  message: string;
  expires_in?: number;
}

export interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (email: string, code: string) => Promise<void>;
  register: (data: RegisterRequest) => Promise<void>;
  logout: () => void;
  sendVerificationCode: (email: string, type: 'login' | 'register') => Promise<void>;
  loginWithGoogle: (credential: string) => Promise<void>;
} 