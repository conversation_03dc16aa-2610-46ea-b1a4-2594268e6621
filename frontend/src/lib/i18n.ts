'use client'

import { useTranslation as useI18nTranslation } from 'react-i18next'
import { useEffect } from 'react'
import './i18n-client' // Initialize i18n

export const useTranslation = (namespace: string = 'common') => {
  return useI18nTranslation(namespace)
}

export const useLanguage = () => {
  const { i18n } = useI18nTranslation()
  
  const changeLanguage = (locale: string) => {
    i18n.changeLanguage(locale)
  }

  const getCurrentLanguage = () => {
    return i18n.language || 'zh'
  }

  const getLanguageName = (locale: string) => {
    const names: Record<string, string> = {
      zh: '中文',
      en: 'English',
      ja: '日本語',
      ko: '한국어'
    }
    return names[locale] || locale
  }

  const getLanguageFlag = (locale: string) => {
    const flags: Record<string, string> = {
      zh: '🇨🇳',
      en: '🇺🇸',
      ja: '🇯🇵',
      ko: '🇰🇷'
    }
    return flags[locale] || '🌐'
  }

  // Auto-detect user's location and set language
  useEffect(() => {
    // 确保只在客户端运行
    if (typeof window === 'undefined') return;

    const detectAndSetLanguage = async () => {
      try {
        // 检查是否已有存储的语言设置
        const storedLanguage = localStorage.getItem('i18nextLng');
        if (storedLanguage) {
          return; // 已有设置，不需要检测
        }

        // Check if geolocation is available
        if ('geolocation' in navigator) {
          navigator.geolocation.getCurrentPosition(
            async (position) => {
              const { latitude, longitude } = position.coords
              
              // Use a free geolocation API to get country code
              try {
                const response = await fetch(`https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${latitude}&longitude=${longitude}&localityLanguage=en`)
                const data = await response.json()
                
                if (data.countryCode) {
                  const countryToLanguage: Record<string, string> = {
                    'CN': 'zh',
                    'TW': 'zh',
                    'HK': 'zh',
                    'US': 'en',
                    'GB': 'en',
                    'CA': 'en',
                    'AU': 'en',
                    'JP': 'ja',
                    'KR': 'ko',
                  }
                  
                  const detectedLanguage = countryToLanguage[data.countryCode]
                  if (detectedLanguage) {
                    changeLanguage(detectedLanguage)
                  }
                }
              } catch (error) {
                console.warn('Failed to detect location-based language:', error)
              }
            },
            (error) => {
              console.warn('Geolocation access denied:', error)
              // Fallback to browser language detection (already handled by i18next)
            },
            { timeout: 5000, maximumAge: 300000 } // 5 second timeout, cache for 5 minutes
          )
        }
      } catch (error) {
        console.warn('Geolocation not available:', error)
      }
    }

    // 延迟执行以避免水合问题
    const timer = setTimeout(() => {
      detectAndSetLanguage()
    }, 100)

    return () => clearTimeout(timer)
  }, [])

  return {
    currentLanguage: getCurrentLanguage(),
    changeLanguage,
    getLanguageName,
    getLanguageFlag,
    availableLocales: ['zh', 'en', 'ja', 'ko']
  }
} 