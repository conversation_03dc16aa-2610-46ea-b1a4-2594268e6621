import axios from 'axios';
import type {
  APITool,
  APIToolCategory,
  APICallResponse,
  TextBeautifyRequest,
  LanguageDetectRequest,
  TextToImageRequest,
  APIResponse,
  PaginatedResponse,
} from '@/types/api';
import type {
  AuthResponse,
  LoginRequest,
  RegisterRequest,
  SendCodeRequest,
  VerificationCodeResponse,
  User,
} from '@/types/auth';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

// 创建axios实例
const apiClient = axios.create({
  baseURL: `${API_BASE_URL}/api/v1`,
  timeout: 60000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    // 添加认证token（只在客户端）
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem('token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // 统一错误处理
    if (error.response?.status === 401 && typeof window !== 'undefined') {
      // 清除过期token
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      // 跳转到登录页
      window.location.href = '/auth/login';
    }
    console.error('API Error:', error);
    return Promise.reject(error);
  }
);

// 认证相关API
export const authAPI = {
  // 发送验证码
  sendVerificationCode: async (data: SendCodeRequest): Promise<VerificationCodeResponse> => {
    const response = await apiClient.post('/auth/send-code', data);
    return response.data;
  },

  // 邮箱验证码登录
  login: async (data: LoginRequest): Promise<AuthResponse> => {
    const response = await apiClient.post('/auth/login', data);
    return response.data;
  },

  // 邮箱验证码注册
  register: async (data: RegisterRequest): Promise<AuthResponse> => {
    const response = await apiClient.post('/auth/register', data);
    return response.data;
  },

  // 谷歌OAuth登录
  googleLogin: async (credential: string): Promise<AuthResponse> => {
    const response = await apiClient.post('/auth/google', { credential });
    return response.data;
  },

  // 刷新token
  refreshToken: async (): Promise<AuthResponse> => {
    const response = await apiClient.post('/auth/refresh');
    return response.data;
  },

  // 获取当前用户信息
  getCurrentUser: async (): Promise<User> => {
    const response = await apiClient.get('/auth/me');
    return response.data;
  },

  // 登出
  logout: async (): Promise<void> => {
    await apiClient.post('/auth/logout');
  },
};

// API工具相关接口
export const apiToolsAPI = {
  // 获取工具列表
  getTools: async (params?: {
    page?: number;
    limit?: number;
    category?: string;
  }): Promise<PaginatedResponse<APITool>> => {
    const response = await apiClient.get('/ai-tools/', { params });
    return response.data;
  },

  // 获取工具分类
  getCategories: async (): Promise<APIResponse<APIToolCategory[]>> => {
    const response = await apiClient.get('/ai-tools/categories');
    return response.data;
  },

  // 文本美化
  beautifyText: async (data: TextBeautifyRequest): Promise<APICallResponse> => {
    const response = await apiClient.post('/ai-tools/text-beautify', data);
    return response.data;
  },

  // 语言检测
  detectLanguage: async (data: LanguageDetectRequest): Promise<APICallResponse> => {
    const response = await apiClient.post('/ai-tools/language-detect', data);
    return response.data;
  },

  // 文本生成图片
  textToImage: async (data: TextToImageRequest): Promise<APICallResponse> => {
    const response = await apiClient.post('/ai-tools/text-to-image', data);
    return response.data;
  },
};

// 用户相关API
export const userAPI = {
  // 获取用户资料
  getProfile: async (): Promise<User> => {
    const response = await apiClient.get('/user/profile');
    return response.data;
  },

  // 更新用户资料
  updateProfile: async (data: Partial<User>): Promise<User> => {
    const response = await apiClient.put('/user/profile', data);
    return response.data;
  },

  // 获取用户积分
  getCredits: async () => {
    const response = await apiClient.get('/user/credits');
    return response.data;
  },

  // 充值积分
  rechargeCredits: async (amount: number) => {
    const response = await apiClient.post('/user/credits/recharge', { amount });
    return response.data;
  },

  // 获取用户活动记录
  getActivities: async () => {
    const response = await apiClient.get('/user/activities');
    return response.data;
  },
};

export default apiClient; 