'use client'

import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'
import LanguageDetector from 'i18next-browser-languagedetector'

// Import translations
import zhCommon from '../../public/locales/zh/common.json'
import enCommon from '../../public/locales/en/common.json'
import jaCommon from '../../public/locales/ja/common.json'
import koCommon from '../../public/locales/ko/common.json'

const resources = {
  zh: {
    common: zhCommon,
  },
  en: {
    common: enCommon,
  },
  ja: {
    common: jaCommon,
  },
  ko: {
    common: koCommon,
  },
}

// Initialize i18n only on client side
if (typeof window !== 'undefined') {
  i18n
    .use(LanguageDetector)
    .use(initReactI18next)
    .init({
      resources,
      fallbackLng: 'zh',
      debug: process.env.NODE_ENV === 'development',
      
      detection: {
        order: ['localStorage', 'navigator', 'htmlTag'],
        caches: ['localStorage'],
      },

      interpolation: {
        escapeValue: false,
      },

      react: {
        useSuspense: false,
      },
    })
} else {
  // Server-side initialization with default language
  i18n
    .use(initReactI18next)
    .init({
      resources,
      lng: 'zh',
      fallbackLng: 'zh',
      debug: false,

      interpolation: {
        escapeValue: false,
      },

      react: {
        useSuspense: false,
      },
    })
}

export default i18n 