'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Menu, Transition } from '@headlessui/react';
import { Fragment } from 'react';
import { ChevronDownIcon, UserIcon, CreditCardIcon, LogOutIcon, MenuIcon, XIcon } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useTranslation } from '@/lib/i18n';
import LanguageSwitcher from '@/components/LanguageSwitcher';
import ClientOnly from '@/components/ClientOnly';
import TranslatedText from '@/components/TranslatedText';

export default function Header() {
  const { user, isAuthenticated, logout } = useAuth();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const { t } = useTranslation();

  return (
    <header className="bg-white border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">AI</span>
              </div>
              <span className="text-xl font-bold text-gray-900">AI Tools</span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-8">
            <Link href="/api-tools" className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
              <TranslatedText tKey="nav.tools" fallback="AI Tools" />
            </Link>
            <Link href="/pricing" className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
              <TranslatedText tKey="nav.pricing" fallback="Pricing" />
            </Link>
            <Link href="/blog" className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
              <TranslatedText tKey="nav.blog" fallback="Blog" />
            </Link>
            {isAuthenticated && (
              <Link href="/account" className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                <TranslatedText tKey="nav.profile" fallback="Profile" />
              </Link>
            )}
          </nav>

          {/* Desktop Actions */}
          <div className="hidden md:flex items-center space-x-4">
            {/* 语言切换器 */}
            <ClientOnly>
              <LanguageSwitcher />
            </ClientOnly>
            
            <ClientOnly fallback={
              <div className="text-sm text-gray-600">
                Loading...
              </div>
            }>
              {isAuthenticated ? (
              <>
                {/* 积分显示 */}
                <div className="text-sm text-gray-600">
                  <TranslatedText tKey="nav.credits" fallback="Credits" />: <span className="font-semibold text-primary-600">{user?.credits || 0}</span>
                </div>
                
                {/* 用户菜单 */}
                <Menu as="div" className="relative inline-block text-left">
                  <div>
                    <Menu.Button className="inline-flex justify-center w-full px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                      <div className="flex items-center space-x-2">
                        {user?.avatar_url ? (
                          <img
                            className="w-6 h-6 rounded-full"
                            src={user.avatar_url}
                            alt={user.username}
                          />
                        ) : (
                          <UserIcon className="w-5 h-5" />
                        )}
                        <span>{user?.username}</span>
                        <ChevronDownIcon className="w-4 h-4" />
                      </div>
                    </Menu.Button>
                  </div>

                  <Transition
                    as={Fragment}
                    enter="transition ease-out duration-100"
                    enterFrom="transform opacity-0 scale-95"
                    enterTo="transform opacity-100 scale-100"
                    leave="transition ease-in duration-75"
                    leaveFrom="transform opacity-100 scale-100"
                    leaveTo="transform opacity-0 scale-95"
                  >
                    <Menu.Items className="absolute right-0 w-56 mt-2 origin-top-right bg-white border border-gray-200 divide-y divide-gray-100 rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                      <div className="px-1 py-1">
                        <Menu.Item>
                          {({ active }) => (
                            <Link
                              href="/account"
                              className={`${
                                active ? 'bg-primary-500 text-white' : 'text-gray-900'
                              } group flex rounded-md items-center w-full px-2 py-2 text-sm`}
                            >
                              <UserIcon className="w-5 h-5 mr-2" />
                              <TranslatedText tKey="nav.profile" fallback="Profile" />
                            </Link>
                          )}
                        </Menu.Item>
                        <Menu.Item>
                          {({ active }) => (
                            <Link
                              href="/account/credits"
                              className={`${
                                active ? 'bg-primary-500 text-white' : 'text-gray-900'
                              } group flex rounded-md items-center w-full px-2 py-2 text-sm`}
                            >
                              <CreditCardIcon className="w-5 h-5 mr-2" />
                              <TranslatedText tKey="nav.credits" fallback="Credits" />
                            </Link>
                          )}
                        </Menu.Item>
                      </div>
                      <div className="px-1 py-1">
                        <Menu.Item>
                          {({ active }) => (
                            <button
                              onClick={logout}
                              className={`${
                                active ? 'bg-red-500 text-white' : 'text-gray-900'
                              } group flex rounded-md items-center w-full px-2 py-2 text-sm`}
                            >
                              <LogOutIcon className="w-5 h-5 mr-2" />
                              <TranslatedText tKey="nav.logout" fallback="Logout" />
                            </button>
                          )}
                        </Menu.Item>
                      </div>
                    </Menu.Items>
                  </Transition>
                </Menu>
              </>
            ) : (
              <>
                <Link
                  href="/auth/login"
                  className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
                >
                  <TranslatedText tKey="nav.login" fallback="Login" />
                </Link>
                <Link
                  href="/auth/register"
                  className="bg-primary-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-primary-700"
                >
                  <TranslatedText tKey="nav.register" fallback="Register" />
                </Link>
              </>
            )}
            </ClientOnly>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              className="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500"
            >
              {mobileMenuOpen ? (
                <XIcon className="block h-6 w-6" />
              ) : (
                <MenuIcon className="block h-6 w-6" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile menu */}
        {mobileMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
              {/* 移动端语言切换器 */}
              <div className="px-3 py-2">
                <ClientOnly>
                  <LanguageSwitcher />
                </ClientOnly>
              </div>
              
              <Link
                href="/api-tools"
                className="text-gray-600 hover:text-gray-900 block px-3 py-2 rounded-md text-base font-medium"
                onClick={() => setMobileMenuOpen(false)}
              >
                <TranslatedText tKey="nav.tools" fallback="AI Tools" />
              </Link>
              <Link
                href="/pricing"
                className="text-gray-600 hover:text-gray-900 block px-3 py-2 rounded-md text-base font-medium"
                onClick={() => setMobileMenuOpen(false)}
              >
                <TranslatedText tKey="nav.pricing" fallback="Pricing" />
              </Link>
              <Link
                href="/blog"
                className="text-gray-600 hover:text-gray-900 block px-3 py-2 rounded-md text-base font-medium"
                onClick={() => setMobileMenuOpen(false)}
              >
                <TranslatedText tKey="nav.blog" fallback="Blog" />
              </Link>
              <ClientOnly fallback={
                <div className="border-t border-gray-200 pt-3">
                  <div className="px-3 py-2">
                    <div className="text-sm text-gray-500">Loading...</div>
                  </div>
                </div>
              }>
                {isAuthenticated ? (
                  <>
                    <Link
                      href="/account"
                      className="text-gray-600 hover:text-gray-900 block px-3 py-2 rounded-md text-base font-medium"
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      <TranslatedText tKey="nav.profile" fallback="Profile" />
                    </Link>
                    <div className="border-t border-gray-200 pt-3">
                      <div className="px-3 py-2">
                        <div className="text-sm text-gray-500">已登录为</div>
                        <div className="text-base font-medium text-gray-900">{user?.username}</div>
                        <div className="text-sm text-gray-500"><TranslatedText tKey="nav.credits" fallback="Credits" />: {user?.credits || 0}</div>
                      </div>
                      <button
                        onClick={() => {
                          logout();
                          setMobileMenuOpen(false);
                        }}
                        className="block w-full text-left px-3 py-2 text-base font-medium text-red-600 hover:text-red-500"
                      >
                        <TranslatedText tKey="nav.logout" fallback="Logout" />
                      </button>
                    </div>
                  </>
                ) : (
                  <div className="border-t border-gray-200 pt-3 space-y-1">
                    <Link
                      href="/auth/login"
                      className="block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-gray-900"
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      <TranslatedText tKey="nav.login" fallback="Login" />
                    </Link>
                    <Link
                      href="/auth/register"
                      className="block px-3 py-2 rounded-md text-base font-medium bg-primary-600 text-white hover:bg-primary-700"
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      <TranslatedText tKey="nav.register" fallback="Register" />
                    </Link>
                  </div>
                )}
              </ClientOnly>
            </div>
          </div>
        )}
      </div>
    </header>
  );
} 