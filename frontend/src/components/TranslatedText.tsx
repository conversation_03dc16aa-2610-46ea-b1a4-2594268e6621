'use client';

import { useTranslation } from '@/lib/i18n';
import ClientOnly from './ClientOnly';

interface TranslatedTextProps {
  tKey: string;
  namespace?: string;
  fallback?: string;
  className?: string;
}

export default function TranslatedText({ 
  tKey, 
  namespace = 'common', 
  fallback,
  className 
}: TranslatedTextProps) {
  const { t } = useTranslation(namespace);
  
  return (
    <ClientOnly fallback={<span className={className}>{fallback || tKey}</span>}>
      <span className={className}>{t(tKey)}</span>
    </ClientOnly>
  );
} 