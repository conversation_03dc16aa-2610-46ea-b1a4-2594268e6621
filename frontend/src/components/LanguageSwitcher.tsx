'use client'

import { Fragment } from 'react'
import { Menu, Transition } from '@headlessui/react'
import { ChevronDownIcon, GlobeIcon } from 'lucide-react'
import { useLanguage } from '@/lib/i18n'

export default function LanguageSwitcher() {
  const { currentLanguage, changeLanguage, getLanguageName, getLanguageFlag, availableLocales } = useLanguage()

  return (
    <Menu as="div" className="relative inline-block text-left">
      <div>
        <Menu.Button className="inline-flex items-center justify-center w-full px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
          <GlobeIcon className="w-4 h-4 mr-2" />
          <span className="mr-1">{getLanguageFlag(currentLanguage)}</span>
          <span>{getLanguageName(currentLanguage)}</span>
          <ChevronDownIcon className="w-4 h-4 ml-2" />
        </Menu.Button>
      </div>

      <Transition
        as={Fragment}
        enter="transition ease-out duration-100"
        enterFrom="transform opacity-0 scale-95"
        enterTo="transform opacity-100 scale-100"
        leave="transition ease-in duration-75"
        leaveFrom="transform opacity-100 scale-100"
        leaveTo="transform opacity-0 scale-95"
      >
        <Menu.Items className="absolute right-0 w-48 mt-2 origin-top-right bg-white border border-gray-200 divide-y divide-gray-100 rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none z-50">
          <div className="px-1 py-1">
            {availableLocales.map((locale) => (
              <Menu.Item key={locale}>
                {({ active }) => (
                  <button
                    onClick={() => changeLanguage(locale)}
                    className={`${
                      active ? 'bg-primary-500 text-white' : 'text-gray-900'
                    } ${
                      currentLanguage === locale ? 'bg-primary-100 text-primary-900' : ''
                    } group flex rounded-md items-center w-full px-2 py-2 text-sm`}
                  >
                    <span className="mr-3 text-lg">{getLanguageFlag(locale)}</span>
                    <span>{getLanguageName(locale)}</span>
                    {currentLanguage === locale && (
                      <span className="ml-auto text-primary-600">✓</span>
                    )}
                  </button>
                )}
              </Menu.Item>
            ))}
          </div>
        </Menu.Items>
      </Transition>
    </Menu>
  )
} 