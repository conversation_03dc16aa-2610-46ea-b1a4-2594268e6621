'use client';

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Mail, User, UserPlus, Timer, AlertCircle } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import GoogleSignIn from './GoogleSignIn';

const registerSchema = z.object({
  email: z.string().email('请输入有效的邮箱地址'),
  username: z.string().min(3, '用户名至少3个字符').max(20, '用户名最多20个字符'),
  full_name: z.string().min(2, '姓名至少2个字符').optional().or(z.literal('')),
  verification_code: z.string().min(6, '验证码长度至少6位'),
});

type RegisterFormData = z.infer<typeof registerSchema>;

interface RegisterFormProps {
  onSwitchToLogin: () => void;
}

export default function RegisterForm({ onSwitchToLogin }: RegisterFormProps) {
  const { register: registerUser, sendVerificationCode, isLoading } = useAuth();
  const [countdown, setCountdown] = useState(0);
  const [codeSent, setCodeSent] = useState(false);

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors, isValid },
  } = useForm<RegisterFormData>({
    resolver: zodResolver(registerSchema),
    mode: 'onChange',
  });

  const email = watch('email');

  // 倒计时效果
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [countdown]);

  const handleSendCode = async () => {
    if (!email || !/\S+@\S+\.\S+/.test(email)) {
      return;
    }

    try {
      await sendVerificationCode(email, 'register');
      setCodeSent(true);
      setCountdown(60);
    } catch (error) {
      // 错误已在context中处理
    }
  };

  const onSubmit = async (data: RegisterFormData) => {
    try {
      await registerUser({
        email: data.email,
        username: data.username,
        full_name: data.full_name || undefined,
        verification_code: data.verification_code,
      });
    } catch (error) {
      // 错误已在context中处理
    }
  };

  return (
    <div className="w-full max-w-md mx-auto">
      <div className="bg-white rounded-2xl shadow-xl p-8">
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4">
            <UserPlus className="w-8 h-8 text-green-600" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">创建账户</h2>
          <p className="text-gray-600">使用验证码快速注册</p>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* 邮箱输入 */}
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
              邮箱地址 *
            </label>
            <div className="relative">
              <Mail className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
              <input
                {...register('email')}
                type="email"
                className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent outline-none transition-colors ${
                  errors.email ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="请输入您的邮箱"
              />
            </div>
            {errors.email && (
              <p className="mt-1 text-sm text-red-600 flex items-center">
                <AlertCircle className="w-4 h-4 mr-1" />
                {errors.email.message}
              </p>
            )}
          </div>

          {/* 用户名输入 */}
          <div>
            <label htmlFor="username" className="block text-sm font-medium text-gray-700 mb-2">
              用户名 *
            </label>
            <div className="relative">
              <User className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
              <input
                {...register('username')}
                type="text"
                className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent outline-none transition-colors ${
                  errors.username ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="输入用户名（3-20个字符）"
              />
            </div>
            {errors.username && (
              <p className="mt-1 text-sm text-red-600 flex items-center">
                <AlertCircle className="w-4 h-4 mr-1" />
                {errors.username.message}
              </p>
            )}
          </div>

          {/* 姓名输入（可选） */}
          <div>
            <label htmlFor="full_name" className="block text-sm font-medium text-gray-700 mb-2">
              真实姓名（可选）
            </label>
            <div className="relative">
              <User className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
              <input
                {...register('full_name')}
                type="text"
                className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent outline-none transition-colors ${
                  errors.full_name ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="输入您的真实姓名"
              />
            </div>
            {errors.full_name && (
              <p className="mt-1 text-sm text-red-600 flex items-center">
                <AlertCircle className="w-4 h-4 mr-1" />
                {errors.full_name.message}
              </p>
            )}
          </div>

          {/* 验证码输入 */}
          <div>
            <label htmlFor="verification_code" className="block text-sm font-medium text-gray-700 mb-2">
              验证码 *
            </label>
            <div className="flex space-x-3">
              <div className="flex-1 relative">
                <Timer className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                <input
                  {...register('verification_code')}
                  type="text"
                  maxLength={6}
                  className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent outline-none transition-colors ${
                    errors.verification_code ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="输入6位验证码"
                />
              </div>
              <button
                type="button"
                onClick={handleSendCode}
                disabled={!email || !/\S+@\S+\.\S+/.test(email) || countdown > 0}
                className={`px-4 py-3 rounded-lg font-medium transition-colors ${
                  countdown > 0
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    : 'bg-green-600 text-white hover:bg-green-700'
                }`}
              >
                {countdown > 0 ? `${countdown}s` : codeSent ? '重新发送' : '发送验证码'}
              </button>
            </div>
            {errors.verification_code && (
              <p className="mt-1 text-sm text-red-600 flex items-center">
                <AlertCircle className="w-4 h-4 mr-1" />
                {errors.verification_code.message}
              </p>
            )}
          </div>

          {/* 注册按钮 */}
          <button
            type="submit"
            disabled={!isValid || isLoading}
            className={`w-full py-3 px-4 rounded-lg font-medium transition-colors ${
              !isValid || isLoading
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-green-600 text-white hover:bg-green-700'
            }`}
          >
            {isLoading ? (
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                注册中...
              </div>
            ) : (
              '创建账户'
            )}
          </button>
        </form>

        {/* 分隔线 */}
        <div className="mt-6 mb-6">
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-300" />
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-white text-gray-500">或</span>
            </div>
          </div>
        </div>

        {/* 谷歌登录 */}
        <GoogleSignIn />

        {/* 切换到登录 */}
        <div className="mt-6 text-center">
          <p className="text-gray-600">
            已有账户？{' '}
            <button
              onClick={onSwitchToLogin}
              className="text-green-600 hover:text-green-700 font-medium"
            >
              立即登录
            </button>
          </p>
        </div>

        {/* 用户协议 */}
        <div className="mt-4 text-center">
          <p className="text-xs text-gray-500">
            注册即表示您同意我们的{' '}
            <a href="/terms" className="text-green-600 hover:text-green-700">
              服务条款
            </a>{' '}
            和{' '}
            <a href="/privacy" className="text-green-600 hover:text-green-700">
              隐私政策
            </a>
          </p>
        </div>
      </div>
    </div>
  );
} 