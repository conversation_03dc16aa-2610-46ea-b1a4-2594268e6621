'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { APITool } from '@/types/api';
import Card from '@/components/ui/Card';

interface ToolCardProps {
  tool: APITool;
  onClick?: (tool: APITool) => void;
}

export default function ToolCard({ tool, onClick }: ToolCardProps) {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  // 生成工具的slug，如果没有则从名称生成
  const getToolSlug = (tool: APITool) => {
    if ('slug' in tool && tool.slug) {
      return tool.slug;
    }
    // 从名称生成slug
    const slugMap: { [key: string]: string } = {
      '文本美化': 'text-beautify',
      '语言检测': 'language-detect',
      '文本生成图片': 'text-to-image'
    };
    return slugMap[tool.name] || tool.name.toLowerCase().replace(/\s+/g, '-');
  };

  const slug = getToolSlug(tool);

  const cardContent = (
    <div className="flex items-start space-x-4">
      <div className="text-3xl">{tool.icon}</div>
      <div className="flex-1">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">{tool.name}</h3>
        <p className="text-gray-600 text-sm mb-3">{tool.description}</p>
        <div className="flex items-center justify-between">
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
            {tool.category}
          </span>
          <div className="text-right">
            <div className="text-sm text-gray-500">消耗积分</div>
            <div className="text-lg font-semibold text-primary-600">{tool.cost_per_request}</div>
          </div>
        </div>
      </div>
    </div>
  );

  // 防止水合错误
  if (!isMounted) {
    return (
      <Card>
        <div className="flex items-start space-x-4">
          <div className="w-12 h-12 bg-gray-200 rounded animate-pulse"></div>
          <div className="flex-1">
            <div className="h-6 bg-gray-200 rounded mb-2 animate-pulse"></div>
            <div className="h-4 bg-gray-200 rounded mb-3 animate-pulse"></div>
            <div className="flex items-center justify-between">
              <div className="w-16 h-6 bg-gray-200 rounded animate-pulse"></div>
              <div className="text-right">
                <div className="w-12 h-4 bg-gray-200 rounded mb-1 animate-pulse"></div>
                <div className="w-8 h-6 bg-gray-200 rounded animate-pulse"></div>
              </div>
            </div>
          </div>
        </div>
      </Card>
    );
  }

  if (onClick) {
    return (
      <div className="cursor-pointer hover:shadow-md transition-shadow duration-200" onClick={() => onClick(tool)}>
        <Card>
          {cardContent}
        </Card>
      </div>
    );
  }

  return (
    <Link href={`/tools/${slug}`} className="cursor-pointer hover:shadow-md transition-shadow duration-200 block">
      <Card>
        {cardContent}
      </Card>
    </Link>
  );
} 