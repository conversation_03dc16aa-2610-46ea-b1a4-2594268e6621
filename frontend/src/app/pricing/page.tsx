'use client';

export default function PricingPage() {
  const plans = [
    {
      name: '免费版',
      price: '¥0',
      period: '永久免费',
      credits: 100,
      features: [
        '每月100积分',
        '基础API访问',
        '社区支持',
        '标准响应速度'
      ],
      popular: false
    },
    {
      name: '专业版',
      price: '¥99',
      period: '每月',
      credits: 2000,
      features: [
        '每月2000积分',
        '全部API访问',
        '优先技术支持',
        '更快响应速度',
        '使用统计分析'
      ],
      popular: true
    },
    {
      name: '企业版',
      price: '¥299',
      period: '每月',
      credits: 10000,
      features: [
        '每月10000积分',
        '全部API访问',
        '专属技术支持',
        '最快响应速度',
        '详细使用分析',
        '自定义集成'
      ],
      popular: false
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            选择适合您的方案
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            灵活的定价方案，满足个人开发者到企业级应用的不同需求
          </p>
        </div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          {plans.map((plan, index) => (
            <div
              key={index}
              className={`relative bg-white rounded-2xl shadow-lg p-8 ${
                plan.popular ? 'ring-2 ring-primary-500 scale-105' : ''
              }`}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <span className="bg-primary-500 text-white px-4 py-1 rounded-full text-sm font-medium">
                    最受欢迎
                  </span>
                </div>
              )}
              
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-2">{plan.name}</h3>
                <div className="mb-4">
                  <span className="text-4xl font-bold text-gray-900">{plan.price}</span>
                  <span className="text-gray-600 ml-2">{plan.period}</span>
                </div>
                <div className="text-primary-600 font-semibold">
                  {plan.credits.toLocaleString()} 积分/月
                </div>
              </div>

              <ul className="space-y-4 mb-8">
                {plan.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-center">
                    <svg className="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span className="text-gray-600">{feature}</span>
                  </li>
                ))}
              </ul>

              <button
                className={`w-full py-3 rounded-lg font-medium transition-colors ${
                  plan.popular
                    ? 'bg-primary-600 text-white hover:bg-primary-700'
                    : 'bg-gray-100 text-gray-900 hover:bg-gray-200'
                }`}
              >
                {plan.price === '¥0' ? '免费开始' : '立即订阅'}
              </button>
            </div>
          ))}
        </div>

        {/* FAQ Section */}
        <div className="max-w-3xl mx-auto">
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-12">
            常见问题
          </h2>
          
          <div className="space-y-8">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                什么是积分？
              </h3>
              <p className="text-gray-600">
                积分是我们的计费单位。不同的AI工具消耗不同数量的积分，例如文本美化消耗2积分，语言检测消耗1积分，图像生成消耗5积分。
              </p>
            </div>
            
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                积分会过期吗？
              </h3>
              <p className="text-gray-600">
                每月的积分会在月底重置。未使用的积分不会累积到下个月，建议您合理规划使用。
              </p>
            </div>
            
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                可以随时取消订阅吗？
              </h3>
              <p className="text-gray-600">
                是的，您可以随时取消订阅。取消后，您仍可以使用当前计费周期内剩余的积分。
              </p>
            </div>
            
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                企业版有什么特殊服务？
              </h3>
              <p className="text-gray-600">
                企业版提供专属技术支持、自定义集成服务、详细的使用分析报告，以及更高的API调用限制。
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 