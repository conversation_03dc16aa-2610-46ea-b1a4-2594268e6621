'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { apiToolsAPI } from '@/lib/api';
import { APITool, APIToolCategory } from '@/types/api';
import ToolCard from '@/components/api-tools/ToolCard';
import { useTranslation } from '@/lib/i18n';

export default function APIToolsPage() {
  const { t } = useTranslation();
  const [tools, setTools] = useState<APITool[]>([]);
  const [categories, setCategories] = useState<APIToolCategory[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [selectedTool, setSelectedTool] = useState<APITool | null>(null);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    if (isMounted) {
      loadData();
    }
  }, [selectedCategory, isMounted]);

  const loadData = async () => {
    try {
      setLoading(true);
      const [toolsResponse, categoriesResponse] = await Promise.all([
        apiToolsAPI.getTools({ category: selectedCategory || undefined }),
        apiToolsAPI.getCategories()
      ]);
      
      setTools(toolsResponse.tools);
      setCategories(categoriesResponse.data || []);
    } catch (error) {
      console.error('Failed to load data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleToolClick = (tool: APITool) => {
    setSelectedTool(tool);
  };

  // 生成工具的slug，如果没有则从名称生成
  const getToolSlug = (tool: APITool) => {
    if ('slug' in tool && tool.slug) {
      return tool.slug;
    }
    // 从名称生成slug
    const slugMap: { [key: string]: string } = {
      'Text Beautifier': 'text-beautify',
      'Language Detection': 'language-detect',
      'Text to Image': 'text-to-image',
      'JSON Parser': 'json-parse'
    };
    return slugMap[tool.name] || tool.name.toLowerCase().replace(/\s+/g, '-');
  };

  // 在组件挂载前不渲染任何内容，避免水合错误
  if (!isMounted) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">{t('tools.title')}</h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {t('tools.subtitle')}
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, index) => (
              <div key={index} className="bg-white rounded-lg p-6 animate-pulse">
                <div className="w-12 h-12 bg-gray-200 rounded mb-4"></div>
                <div className="h-6 bg-gray-200 rounded mb-2"></div>
                <div className="h-4 bg-gray-200 rounded mb-4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">{t('tools.title')}</h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {t('tools.subtitle')}
          </p>
        </div>

        {/* Category Filter */}
        <div className="mb-8">
          <div className="flex flex-wrap gap-4 justify-center">
            <button
              onClick={() => setSelectedCategory('')}
              className={`px-6 py-2 rounded-full text-sm font-medium transition-colors ${
                selectedCategory === '' 
                  ? 'bg-primary-600 text-white' 
                  : 'bg-white text-gray-600 hover:bg-gray-100'
              }`}
            >
              {t('tools.allCategories')}
            </button>
            {categories.map((category) => (
              <button
                key={category.name}
                onClick={() => setSelectedCategory(category.name)}
                className={`px-6 py-2 rounded-full text-sm font-medium transition-colors ${
                  selectedCategory === category.name 
                    ? 'bg-primary-600 text-white' 
                    : 'bg-white text-gray-600 hover:bg-gray-100'
                }`}
              >
                {category.icon} {category.name} ({category.count})
              </button>
            ))}
          </div>
        </div>

        {/* Tools Grid */}
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, index) => (
              <div key={index} className="bg-white rounded-lg p-6 animate-pulse">
                <div className="w-12 h-12 bg-gray-200 rounded mb-4"></div>
                <div className="h-6 bg-gray-200 rounded mb-2"></div>
                <div className="h-4 bg-gray-200 rounded mb-4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {tools.map((tool) => (
              <ToolCard
                key={tool.id}
                tool={tool}
              />
            ))}
          </div>
        )}

        {/* Empty State */}
        {!loading && tools.length === 0 && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🔍</div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              {t('tools.noToolsFound')}
            </h3>
            <p className="text-gray-600">
              {t('tools.tryOtherCategories')}
            </p>
          </div>
        )}
      </div>

      {/* Tool Detail Modal */}
      {selectedTool && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto" role="dialog" aria-modal="true">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-3">
                  <span className="text-3xl">{selectedTool.icon}</span>
                  <h2 className="text-2xl font-bold text-gray-900">{selectedTool.name}</h2>
                </div>
                <button
                  onClick={() => setSelectedTool(null)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">{t('tools.example')}</h3>
                  <p className="text-gray-600">{selectedTool.description}</p>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-1">{t('tools.categories')}</h4>
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                      {selectedTool.category}
                    </span>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 mb-1">{t('tools.creditsRequired')}</h4>
                    <span className="text-lg font-semibold text-primary-600">
                      {selectedTool.cost_per_request}
                    </span>
                  </div>
                </div>

                <div className="pt-4">
                  <Link 
                    href={`/tools/${getToolSlug(selectedTool)}`}
                    className="block w-full bg-primary-600 text-white py-3 rounded-lg font-medium hover:bg-primary-700 transition-colors text-center"
                  >
                    {t('tools.tryTool')}
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 