'use client';

import { useState } from 'react';
import { useTranslation } from '@/lib/i18n';

export default function JsonParserPage() {
  const { t } = useTranslation();
  const [inputText, setInputText] = useState('');
  const [formatType, setFormatType] = useState<'pretty' | 'compact' | 'validate'>('pretty');
  const [indent, setIndent] = useState(2);
  const [result, setResult] = useState<{
    isValid: boolean;
    errorMessage?: string;
    originalText?: string;
    parsedJson?: string;
  } | null>(null);
  const [loading, setLoading] = useState(false);

  const handleParse = async () => {
    if (!inputText.trim()) {
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/tools/parse-json`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: inputText,
          format_type: formatType,
          indent: indent,
        }),
      });

      const data = await response.json();
      setResult(data);
    } catch (error) {
      console.error('Error parsing JSON:', error);
      setResult({
        isValid: false,
        errorMessage: 'Network error occurred',
      });
    }
    setLoading(false);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h1 className="text-2xl font-bold text-gray-900">{t('jsonParser.title')}</h1>
            <p className="mt-2 text-gray-600">{t('jsonParser.description')}</p>
          </div>

          <div className="p-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Input Section */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('jsonParser.inputLabel')}
                </label>
                <textarea
                  value={inputText}
                  onChange={(e) => setInputText(e.target.value)}
                  placeholder={t('jsonParser.inputPlaceholder')}
                  className="w-full h-64 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono text-sm"
                />

                {/* Options */}
                <div className="mt-4 space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {t('jsonParser.formatType')}
                    </label>
                    <select
                      value={formatType}
                      onChange={(e) => setFormatType(e.target.value as 'pretty' | 'compact' | 'validate')}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="pretty">{t('jsonParser.formatTypes.pretty')}</option>
                      <option value="compact">{t('jsonParser.formatTypes.compact')}</option>
                      <option value="validate">{t('jsonParser.formatTypes.validate')}</option>
                    </select>
                  </div>

                  {formatType === 'pretty' && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        {t('jsonParser.indent')}
                      </label>
                      <input
                        type="number"
                        min="1"
                        max="8"
                        value={indent}
                        onChange={(e) => setIndent(parseInt(e.target.value) || 2)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                  )}
                </div>

                <button
                  onClick={handleParse}
                  disabled={loading || !inputText.trim()}
                  className="mt-4 w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? t('common.loading') : t('jsonParser.parseButton')}
                </button>
              </div>

              {/* Result Section */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('jsonParser.result')}
                </label>
                
                {result && (
                  <div className="space-y-4">
                    {/* Validity Status */}
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium text-gray-700">
                        {t('jsonParser.isValid')}:
                      </span>
                      <span
                        className={`px-2 py-1 rounded-full text-xs font-medium ${
                          result.isValid
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}
                      >
                        {result.isValid ? t('jsonParser.valid') : t('jsonParser.invalid')}
                      </span>
                    </div>

                    {/* Error Message */}
                    {!result.isValid && result.errorMessage && (
                      <div>
                        <label className="block text-sm font-medium text-red-700 mb-1">
                          {t('jsonParser.errorMessage')}
                        </label>
                        <div className="bg-red-50 border border-red-200 rounded-md p-3">
                          <p className="text-sm text-red-700 font-mono">{result.errorMessage}</p>
                        </div>
                      </div>
                    )}

                    {/* Original Text */}
                    {result.originalText && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          {t('jsonParser.originalText')}
                        </label>
                        <textarea
                          value={result.originalText}
                          readOnly
                          className="w-full h-32 px-3 py-2 border border-gray-300 rounded-md bg-gray-50 font-mono text-sm"
                        />
                      </div>
                    )}

                    {/* Parsed JSON */}
                    {result.parsedJson && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          {t('jsonParser.parsedJson')}
                        </label>
                        <textarea
                          value={result.parsedJson}
                          readOnly
                          className="w-full h-64 px-3 py-2 border border-gray-300 rounded-md bg-gray-50 font-mono text-sm"
                        />
                      </div>
                    )}
                  </div>
                )}

                {!result && (
                  <div className="h-64 flex items-center justify-center border-2 border-dashed border-gray-300 rounded-md">
                    <p className="text-gray-500">{t('jsonParser.inputPlaceholder')}</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 