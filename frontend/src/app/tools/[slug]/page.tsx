'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import apiClient from '@/lib/api';
import { useTranslation } from '@/lib/i18n';

interface ToolDetail {
  id: number;
  name: string;
  slug: string;
  description: string;
  category: string;
  icon: string;
  endpoint: string;
  method: string;
  cost_per_request: number;
  rate_limit: string;
  input_schema: any;
  output_schema: any;
  example_request: any;
  example_response: any;
}

interface ApiResponse {
  success: boolean;
  data: any;
  credits_used?: number;
  execution_time?: number;
  error?: string;
}

export default function ToolDetailPage() {
  const { t } = useTranslation();
  const params = useParams();
  const slug = params?.slug as string;
  
  const [tool, setTool] = useState<ToolDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [requestData, setRequestData] = useState<any>({});
  const [response, setResponse] = useState<ApiResponse | null>(null);
  const [isCallLoading, setIsCallLoading] = useState(false);
  const [lastCallTime, setLastCallTime] = useState<number>(0);
  const [remainingTime, setRemainingTime] = useState<number>(0);

  useEffect(() => {
    if (slug) {
      fetchToolDetail();
    }
  }, [slug]);

  useEffect(() => {
    // 倒计时逻辑
    if (remainingTime > 0) {
      const timer = setInterval(() => {
        setRemainingTime(prev => {
          if (prev <= 1) {
            clearInterval(timer);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
      return () => clearInterval(timer);
    }
  }, [remainingTime]);

  const fetchToolDetail = async () => {
    try {
      const response = await apiClient.get(`/ai-tools/${slug}`);
      if (response.data.success) {
        const toolData = response.data.data;
        setTool(toolData);
        // 初始化请求数据为示例数据
        setRequestData(toolData.example_request || {});
      }
    } catch (error) {
      console.error('获取工具详情失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setRequestData((prev: any) => ({
      ...prev,
      [field]: value
    }));
  };

  const handleApiCall = async () => {
    if (!tool) return;

    // 检查速率限制
    const now = Date.now();
    if (now - lastCallTime < 60000) {
      const remaining = Math.ceil((60000 - (now - lastCallTime)) / 1000);
      setRemainingTime(remaining);
      return;
    }

    setIsCallLoading(true);
    setResponse(null);

    try {
      const response = await apiClient.post(tool.endpoint, requestData);
      setResponse(response.data);
      setLastCallTime(now);
      setRemainingTime(60); // 设置60秒倒计时
    } catch (error: any) {
      if (error.response?.status === 429) {
        // 速率限制错误
        const errorMsg = error.response.data.detail || '请求过于频繁';
        setResponse({
          success: false,
          data: null,
          error: errorMsg
        });
        setRemainingTime(60);
      } else {
        setResponse({
          success: false,
          data: null,
          error: error.response?.data?.detail || '请求失败'
        });
      }
    } finally {
      setIsCallLoading(false);
    }
  };

  const renderInputField = (fieldName: string, fieldSchema: any) => {
    const value = requestData[fieldName] || fieldSchema.default || '';
    
    if (fieldSchema.enum) {
      return (
        <select
          value={value}
          onChange={(e) => handleInputChange(fieldName, e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          {fieldSchema.enum.map((option: string) => (
            <option key={option} value={option}>
              {option}
            </option>
          ))}
        </select>
      );
    }

    if (fieldSchema.type === 'string') {
      return (
        <textarea
          value={value}
          onChange={(e) => handleInputChange(fieldName, e.target.value)}
          placeholder={fieldSchema.example || fieldSchema.description}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 min-h-[100px]"
        />
      );
    }

    return (
      <input
        type="text"
        value={value}
        onChange={(e) => handleInputChange(fieldName, e.target.value)}
        placeholder={fieldSchema.example || fieldSchema.description}
        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
      />
    );
  };

  const renderResponse = () => {
    if (!response) return null;

    if (!response.success) {
      return (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h4 className="text-red-800 font-medium mb-2">{t('tools.error')}</h4>
          <p className="text-red-600">{response.error}</p>
        </div>
      );
    }

    const data = response.data;

    // 如果是图片生成工具，特殊处理
    if (slug === 'text-to-image' && data.image_url) {
      return (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <h4 className="text-green-800 font-medium mb-4">{t('tools.generatedResult')}</h4>
          <div className="space-y-4">
            <div>
              <img 
                src={data.image_url} 
                alt={data.prompt}
                className="max-w-full h-auto rounded-lg shadow-md"
              />
            </div>
            <div className="text-sm text-gray-600">
              <p><strong>{t('tools.prompt')}:</strong> {data.prompt}</p>
              <p><strong>{t('tools.style')}:</strong> {data.style}</p>
              <p><strong>{t('tools.size')}:</strong> {data.size}</p>
            </div>
          </div>
        </div>
      );
    }

    // 其他工具的文本结果
    return (
      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <h4 className="text-green-800 font-medium mb-2">{t('tools.callResult')}</h4>
        <pre className="bg-white p-3 rounded border text-sm overflow-auto">
          {JSON.stringify(data, null, 2)}
        </pre>
        {response.execution_time && (
          <p className="text-sm text-gray-600 mt-2">
            {t('tools.executionTime')}: {response.execution_time}ms
          </p>
        )}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">{t('common.loading')}</p>
        </div>
      </div>
    );
  }

  if (!tool) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">{t('tools.toolNotFound')}</h1>
          <p className="text-gray-600">{t('tools.checkUrl')}</p>
        </div>
      </div>
    );
  }

  const canCall = remainingTime === 0 && !isCallLoading;

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 工具信息 */}
        <div className="mb-8">
          <div className="flex items-center mb-4">
            <span className="text-4xl mr-4">{tool.icon}</span>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">{tool.name}</h1>
              <p className="text-gray-600 mt-1">{tool.description}</p>
            </div>
          </div>
          
          <div className="flex flex-wrap gap-4 text-sm text-gray-600">
            <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded">
              {tool.category}
            </span>
            <span className="bg-green-100 text-green-800 px-2 py-1 rounded">
              {t('tools.creditsRequired')}: {tool.cost_per_request}
            </span>
            <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded">
              {tool.rate_limit}
            </span>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 左侧：API调试窗口 */}
          <div>
            <Card className="p-6">
              <h2 className="text-xl font-semibold mb-4">{t('tools.apiDebugger')}</h2>
              
              {/* 请求参数 */}
              <div className="space-y-4 mb-6">
                <h3 className="font-medium text-gray-900">{t('tools.inputParameters')}</h3>
                {Object.entries(tool.input_schema).map(([fieldName, fieldSchema]: [string, any]) => (
                  <div key={fieldName}>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {fieldName}
                      {fieldSchema.required && (
                        <span className="text-red-500 ml-1">*</span>
                      )}
                    </label>
                    <p className="text-xs text-gray-500 mb-2">{fieldSchema.description}</p>
                    {renderInputField(fieldName, fieldSchema)}
                  </div>
                ))}
              </div>

              {/* 调用按钮 */}
              <Button
                onClick={handleApiCall}
                disabled={!canCall}
                className={`w-full ${!canCall ? 'opacity-50 cursor-not-allowed' : ''}`}
              >
                {isCallLoading ? (
                  <span className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    {t('tools.calling')}
                  </span>
                ) : remainingTime > 0 ? (
                  t('tools.waitSeconds', { seconds: remainingTime })
                ) : (
                  t('tools.callApi')
                )}
              </Button>

              {remainingTime > 0 && (
                <p className="text-sm text-orange-600 mt-2 text-center">
                  {t('tools.rateLimitNotice')}
                </p>
              )}
            </Card>
          </div>

          {/* 右侧：结果展示 */}
          <div>
            <Card className="p-6">
              <h2 className="text-xl font-semibold mb-4">{t('tools.callResult')}</h2>
              
              {response ? (
                renderResponse()
              ) : (
                <div className="text-center text-gray-500 py-8">
                  <p>{t('tools.clickToTest')}</p>
                </div>
              )}
            </Card>

            {/* 示例 */}
            <Card className="p-6 mt-6">
              <h2 className="text-xl font-semibold mb-4">{t('tools.example')}</h2>
              
              <div className="space-y-4">
                <div>
                  <h3 className="font-medium text-gray-900 mb-2">{t('tools.requestExample')}</h3>
                  <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto">
                    {JSON.stringify(tool.example_request, null, 2)}
                  </pre>
                </div>
                
                <div>
                  <h3 className="font-medium text-gray-900 mb-2">{t('tools.responseExample')}</h3>
                  <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto">
                    {JSON.stringify(tool.example_response, null, 2)}
                  </pre>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
} 