import { render, screen, waitFor } from '@testing-library/react';
import ToolDetailPage from '../page';
import apiClient from '@/lib/api';

// Mock API client
jest.mock('@/lib/api', () => ({
  __esModule: true,
  default: {
    get: jest.fn(),
    post: jest.fn(),
  },
}));

// Mock Next.js useParams
jest.mock('next/navigation', () => ({
  useParams: () => ({ slug: 'text-beautify' }),
}));

const mockApiClient = apiClient as jest.Mocked<typeof apiClient>;

const mockToolDetail = {
  id: 1,
  name: '文本美化',
  slug: 'text-beautify',
  description: '使用AI技术美化和改进文本表达',
  category: '文本处理',
  icon: '✨',
  endpoint: '/api/v1/ai-tools/text-beautify',
  method: 'POST',
  cost_per_request: 2.0,
  rate_limit: '每分钟1次（未登录用户）',
  input_schema: {
    type: 'object',
    properties: {
      text: {
        type: 'string',
        description: '需要美化的文本',
        example: '这个产品很好用'
      },
      style: {
        type: 'string',
        enum: ['professional', 'casual', 'formal', 'creative'],
        default: 'professional',
        description: '美化风格'
      }
    },
    required: ['text']
  },
  output_schema: {
    type: 'object',
    properties: {
      original_text: { type: 'string', description: '原始文本' },
      beautified_text: { type: 'string', description: '美化后的文本' },
      style: { type: 'string', description: '使用的风格' }
    }
  },
  example_request: {
    text: '这个产品很好用',
    style: 'professional'
  },
  example_response: {
    original_text: '这个产品很好用',
    beautified_text: '这款产品具有卓越的实用性和优异的用户体验',
    style: 'professional'
  }
};

describe('ToolDetailPage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup successful API response
    mockApiClient.get.mockResolvedValue({
      data: {
        success: true,
        data: mockToolDetail
      }
    });
  });

  it('should render tool information correctly', async () => {
    render(<ToolDetailPage />);
    
    // Wait for API call to complete
    await waitFor(() => {
      expect(mockApiClient.get).toHaveBeenCalledWith('/ai-tools/text-beautify');
    });

    // Check if tool information is displayed
    await waitFor(() => {
      expect(screen.getByText('文本美化')).toBeInTheDocument();
      expect(screen.getByText('使用AI技术美化和改进文本表达')).toBeInTheDocument();
      expect(screen.getByText('消耗积分: 2')).toBeInTheDocument();
    });
  });

  it('should initialize request data with example data', async () => {
    render(<ToolDetailPage />);
    
    await waitFor(() => {
      expect(mockApiClient.get).toHaveBeenCalled();
    });

    // Check if example data is loaded into form
    await waitFor(() => {
      const textInput = screen.getByDisplayValue('这个产品很好用');
      expect(textInput).toBeInTheDocument();
    });
  });

  it('should handle API errors gracefully', async () => {
    // Mock API error
    mockApiClient.get.mockRejectedValue(new Error('API Error'));
    
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

    render(<ToolDetailPage />);
    
    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith('获取工具详情失败:', expect.any(Error));
    });

    consoleSpy.mockRestore();
  });

  it('should display loading state initially', () => {
    render(<ToolDetailPage />);
    
    expect(screen.getByText('加载中...')).toBeInTheDocument();
  });

  it('should display error message when tool not found', async () => {
    // Mock API response for non-existent tool
    mockApiClient.get.mockResolvedValue({
      data: {
        success: false,
        error: '工具不存在'
      }
    });

    render(<ToolDetailPage />);
    
    await waitFor(() => {
      expect(screen.getByText('工具不存在')).toBeInTheDocument();
    });
  });
}); 