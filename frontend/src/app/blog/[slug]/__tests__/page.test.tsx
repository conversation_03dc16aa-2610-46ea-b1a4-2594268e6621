import { render, screen, waitFor } from '@testing-library/react';
import BlogDetailPage from '../page';
import apiClient from '@/lib/api';

// Mock API client
jest.mock('@/lib/api', () => ({
  __esModule: true,
  default: {
    get: jest.fn(),
  },
}));

// Mock Next.js useParams
jest.mock('next/navigation', () => ({
  useParams: () => ({ slug: 'ai-tools-guide' }),
}));

const mockApiClient = apiClient as jest.Mocked<typeof apiClient>;

const mockBlogPost = {
  id: 1,
  title: 'AI工具使用指南',
  slug: 'ai-tools-guide',
  excerpt: '了解如何有效使用我们的AI工具来提高工作效率',
  content: '# AI工具使用指南\n\n这是一篇关于如何使用AI工具的文章...\n\n## 主要功能\n\n- 文本美化\n- 语言检测\n- 图片生成',
  author: 'AI Tools Team',
  published_at: '2024-01-15T10:00:00Z',
  tags: ['AI', '教程', '工具'],
  read_time: 5
};

describe('BlogDetailPage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup successful API response
    mockApiClient.get.mockResolvedValue({
      data: {
        success: true,
        data: mockBlogPost
      }
    });
  });

  it('should render blog post title and metadata', async () => {
    render(<BlogDetailPage />);
    
    await waitFor(() => {
      expect(mockApiClient.get).toHaveBeenCalledWith('/blog/posts/ai-tools-guide');
    });

    await waitFor(() => {
      expect(screen.getByText('AI工具使用指南')).toBeInTheDocument();
      expect(screen.getByText('AI Tools Team')).toBeInTheDocument();
      expect(screen.getByText('5分钟阅读')).toBeInTheDocument();
    });
  });

  it('should render markdown content correctly', async () => {
    render(<BlogDetailPage />);
    
    await waitFor(() => {
      expect(screen.getByText('主要功能')).toBeInTheDocument();
      expect(screen.getByText('文本美化')).toBeInTheDocument();
      expect(screen.getByText('语言检测')).toBeInTheDocument();
      expect(screen.getByText('图片生成')).toBeInTheDocument();
    });
  });

  it('should display tags', async () => {
    render(<BlogDetailPage />);
    
    await waitFor(() => {
      expect(screen.getByText('AI')).toBeInTheDocument();
      expect(screen.getByText('教程')).toBeInTheDocument();
      expect(screen.getByText('工具')).toBeInTheDocument();
    });
  });

  it('should display loading state initially', () => {
    render(<BlogDetailPage />);
    
    expect(screen.getByText('加载中...')).toBeInTheDocument();
  });

  it('should handle API errors gracefully', async () => {
    mockApiClient.get.mockRejectedValue(new Error('API Error'));
    
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

    render(<BlogDetailPage />);
    
    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith('获取博客文章失败:', expect.any(Error));
    });

    consoleSpy.mockRestore();
  });

  it('should display 404 when post not found', async () => {
    mockApiClient.get.mockResolvedValue({
      data: {
        success: false,
        error: '文章不存在'
      }
    });

    render(<BlogDetailPage />);
    
    await waitFor(() => {
      expect(screen.getByText('文章不存在')).toBeInTheDocument();
    });
  });
}); 