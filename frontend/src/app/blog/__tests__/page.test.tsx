import { render, screen, waitFor } from '@testing-library/react';
import BlogPage from '../page';
import apiClient from '@/lib/api';

// Mock API client
jest.mock('@/lib/api', () => ({
  __esModule: true,
  default: {
    get: jest.fn(),
  },
}));

const mockApiClient = apiClient as jest.Mocked<typeof apiClient>;

const mockBlogPosts = [
  {
    id: 1,
    title: 'AI工具使用指南',
    slug: 'ai-tools-guide',
    excerpt: '了解如何有效使用我们的AI工具来提高工作效率',
    content: '# AI工具使用指南\n\n这是一篇关于如何使用AI工具的文章...',
    author: 'AI Tools Team',
    published_at: '2024-01-15T10:00:00Z',
    tags: ['AI', '教程', '工具'],
    read_time: 5
  },
  {
    id: 2,
    title: '文本美化功能详解',
    slug: 'text-beautify-guide',
    excerpt: '深入了解文本美化功能的使用方法和最佳实践',
    content: '# 文本美化功能详解\n\n文本美化是我们最受欢迎的功能之一...',
    author: 'AI Tools Team',
    published_at: '2024-01-10T14:30:00Z',
    tags: ['文本处理', '美化', 'AI'],
    read_time: 8
  }
];

describe('BlogPage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup successful API response
    mockApiClient.get.mockResolvedValue({
      data: {
        success: true,
        data: {
          posts: mockBlogPosts,
          total: mockBlogPosts.length,
          page: 1,
          limit: 10
        }
      }
    });
  });

  it('should render blog page title and description', async () => {
    render(<BlogPage />);
    
    expect(screen.getByText('技术博客')).toBeInTheDocument();
    expect(screen.getByText(/分享AI工具使用技巧/)).toBeInTheDocument();
  });

  it('should load and display blog posts', async () => {
    render(<BlogPage />);
    
    await waitFor(() => {
      expect(mockApiClient.get).toHaveBeenCalledWith('/blog/posts', { params: { page: 1, limit: 10 } });
    });

    await waitFor(() => {
      expect(screen.getByText('AI工具使用指南')).toBeInTheDocument();
      expect(screen.getByText('文本美化功能详解')).toBeInTheDocument();
    });
  });

  it('should display blog post metadata', async () => {
    render(<BlogPage />);
    
    await waitFor(() => {
      expect(screen.getByText('AI Tools Team')).toBeInTheDocument();
      expect(screen.getByText('5分钟阅读')).toBeInTheDocument();
      expect(screen.getByText('8分钟阅读')).toBeInTheDocument();
    });
  });

  it('should display loading state initially', () => {
    render(<BlogPage />);
    
    expect(screen.getByText('加载中...')).toBeInTheDocument();
  });

  it('should handle API errors gracefully', async () => {
    mockApiClient.get.mockRejectedValue(new Error('API Error'));
    
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

    render(<BlogPage />);
    
    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith('获取博客文章失败:', expect.any(Error));
    });

    consoleSpy.mockRestore();
  });

  it('should display empty state when no posts found', async () => {
    mockApiClient.get.mockResolvedValue({
      data: {
        success: true,
        data: {
          posts: [],
          total: 0,
          page: 1,
          limit: 10
        }
      }
    });

    render(<BlogPage />);
    
    await waitFor(() => {
      expect(screen.getByText('暂无博客文章')).toBeInTheDocument();
    });
  });
}); 