'use client';

import Link from 'next/link';
import { useState, useEffect } from 'react';
import { useTranslation } from '@/lib/i18n';

export default function HomePage() {
  const { t } = useTranslation();
  const [currentFeature, setCurrentFeature] = useState(0);

  const features = [
    {
      title: 'Text Beautifier',
      description: 'Enhance and improve text expression using AI technology with multiple style transformations',
      icon: '✨',
      color: 'from-blue-500 to-purple-600'
    },
    {
      title: 'Language Detection',
      description: 'Automatically detect the language type of text with support for multiple languages',
      icon: '🌐',
      color: 'from-green-500 to-blue-600'
    },
    {
      title: 'Text to Image',
      description: 'Generate high-quality images from text descriptions with support for multiple artistic styles',
      icon: '🎨',
      color: 'from-purple-500 to-pink-600'
    }
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentFeature((prev) => (prev + 1) % features.length);
    }, 3000);
    return () => clearInterval(interval);
  }, [features.length]);

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-blue-50 via-white to-purple-50 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              {t('hero.title')}
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">
                {t('hero.subtitle')}
              </span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              {t('hero.description')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/api-tools" className="bg-primary-600 text-white px-8 py-3 rounded-lg text-lg font-medium hover:bg-primary-700 transition-colors">
                {t('hero.getStarted')}
              </Link>
              <Link href="/pricing" className="border border-gray-300 text-gray-700 px-8 py-3 rounded-lg text-lg font-medium hover:bg-gray-50 transition-colors">
                {t('nav.pricing')}
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              {t('hero.featuresTitle')}
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              {t('hero.featuresSubtitle')}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div
                key={index}
                className={`relative p-8 rounded-2xl bg-gradient-to-br ${feature.color} text-white transform transition-all duration-300 hover:scale-105 ${
                  currentFeature === index ? 'ring-4 ring-blue-300' : ''
                }`}
              >
                <div className="text-4xl mb-4">{feature.icon}</div>
                <h3 className="text-2xl font-bold mb-3">{feature.title}</h3>
                <p className="text-white/90">{feature.description}</p>
                <div className="absolute top-4 right-4">
                  <div className="w-3 h-3 bg-white/30 rounded-full animate-pulse"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-4xl font-bold text-primary-600 mb-2">4+</div>
              <div className="text-gray-600">AI工具</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-primary-600 mb-2">99.9%</div>
              <div className="text-gray-600">服务可用性</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-primary-600 mb-2">&lt;2s</div>
              <div className="text-gray-600">平均响应时间</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-primary-600 mb-2">24/7</div>
              <div className="text-gray-600">技术支持</div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-primary-600 to-purple-600">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            {t('hero.ctaTitle')}
          </h2>
          <p className="text-xl text-white/90 mb-8">
            {t('hero.ctaSubtitle')}
          </p>
          <Link href="/api-tools" className="bg-white text-primary-600 px-8 py-3 rounded-lg text-lg font-medium hover:bg-gray-100 transition-colors">
            {t('hero.tryFree')}
          </Link>
        </div>
      </section>
    </div>
  );
} 