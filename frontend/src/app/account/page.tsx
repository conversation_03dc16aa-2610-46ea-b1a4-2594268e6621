'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import apiClient from '@/lib/api';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';

interface UserData {
  id: number;
  username: string;
  email: string;
  credits: number;
  subscription: {
    plan: string;
    expires_at: string;
    status: string;
  };
  usage_stats: {
    total_requests: number;
    this_month_requests: number;
    favorite_tool: string;
  };
  created_at: string;
}

export default function AccountPage() {
  const [userData, setUserData] = useState<UserData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchUserData();
  }, []);

  const fetchUserData = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiClient.get('/user/profile');
      
      if (response.data.success) {
        setUserData(response.data.data);
      } else {
        setError(response.data.error || '获取用户信息失败');
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
      setError('请先登录');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getPlanDisplayName = (plan: string) => {
    const planNames: { [key: string]: string } = {
      'free': '免费版',
      'basic': '基础版',
      'premium': 'Premium',
      'enterprise': '企业版'
    };
    return planNames[plan] || plan;
  };

  const getStatusColor = (status: string) => {
    const colors: { [key: string]: string } = {
      'active': 'text-green-600 bg-green-100',
      'expired': 'text-red-600 bg-red-100',
      'cancelled': 'text-gray-600 bg-gray-100'
    };
    return colors[status] || 'text-gray-600 bg-gray-100';
  };

  const getStatusText = (status: string) => {
    const statusTexts: { [key: string]: string } = {
      'active': '有效',
      'expired': '已过期',
      'cancelled': '已取消'
    };
    return statusTexts[status] || status;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="space-y-6">
            {[...Array(3)].map((_, index) => (
              <Card key={index} className="p-6 animate-pulse">
                <div className="h-6 bg-gray-200 rounded mb-4"></div>
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              </Card>
            ))}
          </div>
          <div className="text-center mt-8">
            <p className="text-gray-600">加载中...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error || !userData) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🔐</div>
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              {error || '请先登录'}
            </h1>
            <p className="text-gray-600 mb-8">
              您需要登录才能查看账户信息
            </p>
            <div className="space-x-4">
              <Link
                href="/login"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
              >
                登录
              </Link>
              <Link
                href="/register"
                className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors"
              >
                注册
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">我的账户</h1>
          <p className="text-gray-600 mt-2">管理您的账户信息和订阅</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 用户信息 */}
          <div className="lg:col-span-2">
            <Card className="p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">基本信息</h2>
              <div className="space-y-4">
                <div className="flex items-center">
                  <div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center text-white text-xl font-bold">
                    {userData.username.charAt(0).toUpperCase()}
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-gray-900">{userData.username}</h3>
                    <p className="text-gray-600">{userData.email}</p>
                  </div>
                </div>
                <div className="border-t pt-4">
                  <p className="text-sm text-gray-500">
                    注册时间: {formatDate(userData.created_at)}
                  </p>
                </div>
              </div>
            </Card>

            {/* 使用统计 */}
            <Card className="p-6 mt-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">使用统计</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">{userData.usage_stats.total_requests}</div>
                  <div className="text-sm text-gray-600">总调用次数</div>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">{userData.usage_stats.this_month_requests}</div>
                  <div className="text-sm text-gray-600">本月调用</div>
                </div>
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <div className="text-lg font-bold text-purple-600">{userData.usage_stats.favorite_tool}</div>
                  <div className="text-sm text-gray-600">最常用工具</div>
                </div>
              </div>
            </Card>
          </div>

          {/* 右侧边栏 */}
          <div className="space-y-6">
            {/* 积分信息 */}
            <Card className="p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">剩余积分</h2>
              <div className="text-center">
                <div className="text-4xl font-bold text-blue-600 mb-2">{userData.credits}</div>
                <p className="text-gray-600 mb-4">可用积分</p>
                <Button className="w-full">
                  充值积分
                </Button>
              </div>
            </Card>

            {/* 订阅信息 */}
            <Card className="p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">订阅状态</h2>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">当前套餐</span>
                  <span className="font-medium">{getPlanDisplayName(userData.subscription.plan)}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">状态</span>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(userData.subscription.status)}`}>
                    {getStatusText(userData.subscription.status)}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">到期时间</span>
                  <span className="text-sm">{formatDate(userData.subscription.expires_at)}</span>
                </div>
                <div className="pt-3 border-t">
                  <Button className="w-full" variant="outline">
                    管理订阅
                  </Button>
                </div>
              </div>
            </Card>

            {/* 快捷操作 */}
            <Card className="p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">快捷操作</h2>
              <div className="space-y-3">
                <Link
                  href="/api-tools"
                  className="block w-full text-center px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
                >
                  使用AI工具
                </Link>
                <Link
                  href="/blog"
                  className="block w-full text-center px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
                >
                  查看博客
                </Link>
                <Link
                  href="/settings"
                  className="block w-full text-center px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
                >
                  账户设置
                </Link>
              </div>
            </Card>
          </div>
        </div>

        {/* 最近活动 */}
        <Card className="p-6 mt-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">最近活动</h2>
          <div className="space-y-3">
            <div className="flex items-center justify-between py-2 border-b border-gray-100">
              <div>
                <p className="font-medium text-gray-900">使用文本美化工具</p>
                <p className="text-sm text-gray-500">消耗 2 积分</p>
              </div>
              <span className="text-sm text-gray-500">2小时前</span>
            </div>
            <div className="flex items-center justify-between py-2 border-b border-gray-100">
              <div>
                <p className="font-medium text-gray-900">使用语言检测工具</p>
                <p className="text-sm text-gray-500">消耗 1 积分</p>
              </div>
              <span className="text-sm text-gray-500">1天前</span>
            </div>
            <div className="flex items-center justify-between py-2">
              <div>
                <p className="font-medium text-gray-900">充值积分</p>
                <p className="text-sm text-gray-500">获得 100 积分</p>
              </div>
              <span className="text-sm text-gray-500">3天前</span>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
} 