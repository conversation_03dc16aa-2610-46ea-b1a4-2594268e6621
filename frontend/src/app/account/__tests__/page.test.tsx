import { render, screen, waitFor } from '@testing-library/react';
import AccountPage from '../page';
import apiClient from '@/lib/api';

// Mock API client
jest.mock('@/lib/api', () => ({
  __esModule: true,
  default: {
    get: jest.fn(),
  },
}));

const mockApiClient = apiClient as jest.Mocked<typeof apiClient>;

const mockUserData = {
  id: 1,
  username: 'testuser',
  email: '<EMAIL>',
  credits: 150,
  subscription: {
    plan: 'premium',
    expires_at: '2024-12-31T23:59:59Z',
    status: 'active'
  },
  usage_stats: {
    total_requests: 45,
    this_month_requests: 12,
    favorite_tool: 'text-beautify'
  },
  created_at: '2024-01-01T00:00:00Z'
};

describe('AccountPage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup successful API response
    mockApiClient.get.mockResolvedValue({
      data: {
        success: true,
        data: mockUserData
      }
    });
  });

  it('should render user account information', async () => {
    render(<AccountPage />);
    
    await waitFor(() => {
      expect(mockApiClient.get).toHaveBeenCalledWith('/user/profile');
    });

    await waitFor(() => {
      expect(screen.getByText('我的账户')).toBeInTheDocument();
      expect(screen.getByText('testuser')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    });
  });

  it('should display credits information', async () => {
    render(<AccountPage />);
    
    await waitFor(() => {
      expect(screen.getByText('剩余积分')).toBeInTheDocument();
      expect(screen.getByText('150')).toBeInTheDocument();
    });
  });

  it('should display subscription information', async () => {
    render(<AccountPage />);
    
    await waitFor(() => {
      expect(screen.getByText('订阅状态')).toBeInTheDocument();
      expect(screen.getByText('Premium')).toBeInTheDocument();
      expect(screen.getByText(/2024年12月31日/)).toBeInTheDocument();
    });
  });

  it('should display usage statistics', async () => {
    render(<AccountPage />);
    
    await waitFor(() => {
      expect(screen.getByText('使用统计')).toBeInTheDocument();
      expect(screen.getByText('45')).toBeInTheDocument();
      expect(screen.getByText('12')).toBeInTheDocument();
    });
  });

  it('should display loading state initially', () => {
    render(<AccountPage />);
    
    expect(screen.getByText('加载中...')).toBeInTheDocument();
  });

  it('should handle API errors gracefully', async () => {
    mockApiClient.get.mockRejectedValue(new Error('API Error'));
    
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

    render(<AccountPage />);
    
    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith('获取用户信息失败:', expect.any(Error));
    });

    consoleSpy.mockRestore();
  });

  it('should display login prompt when not authenticated', async () => {
    mockApiClient.get.mockResolvedValue({
      data: {
        success: false,
        error: '未登录'
      }
    });

    render(<AccountPage />);
    
    await waitFor(() => {
      expect(screen.getByText('请先登录')).toBeInTheDocument();
    });
  });
}); 