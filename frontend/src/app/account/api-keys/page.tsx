'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useTranslation } from '@/lib/i18n';
import { toast } from 'react-hot-toast';

interface APIKey {
  id: number;
  name: string;
  prefix: string;
  description?: string;
  usage_count: number;
  last_used_at?: string;
  created_at: string;
}

export default function APIKeysPage() {
  const { user, isAuthenticated } = useAuth();
  const { t } = useTranslation();
  const [apiKeys, setApiKeys] = useState<APIKey[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [newKey, setNewKey] = useState<{ name: string; description: string }>({
    name: '',
    description: ''
  });
  const [createdKey, setCreatedKey] = useState<string | null>(null);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    if (isAuthenticated && isMounted) {
      fetchAPIKeys();
    }
  }, [isAuthenticated, isMounted]);

  const fetchAPIKeys = async () => {
    try {
      const token = localStorage.getItem('access_token');
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/v1/api-keys/`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setApiKeys(data.data);
      }
    } catch (error) {
      console.error('获取API Keys失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const createAPIKey = async () => {
    try {
      const token = localStorage.getItem('access_token');
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/v1/api-keys/`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newKey),
      });

      if (response.ok) {
        const data = await response.json();
        setCreatedKey(data.key);
        setNewKey({ name: '', description: '' });
        setShowCreateModal(false);
        fetchAPIKeys();
      } else {
        const errorData = await response.json();
        alert(errorData.detail || '创建API Key失败');
      }
    } catch (error) {
      console.error('创建API Key失败:', error);
      alert('创建API Key失败');
    }
  };

  const deleteAPIKey = async (keyId: number) => {
    if (!confirm(t('apiKeys.deleteConfirm'))) return;

    try {
      const token = localStorage.getItem('access_token');
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/v1/api-keys/${keyId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        fetchAPIKeys();
      } else {
        alert('删除API Key失败');
      }
    } catch (error) {
      console.error('删除API Key失败:', error);
      alert('删除API Key失败');
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    alert('已复制到剪贴板');
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">请先登录</h1>
          <p className="text-gray-600">您需要登录才能管理API Keys</p>
        </div>
      </div>
    );
  }

  if (!isMounted) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <p className="mt-2 text-gray-600">{t('common.loading')}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex justify-between items-center">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">{t('apiKeys.title')}</h1>
                <p className="mt-2 text-gray-600">
                  {t('apiKeys.subtitle')}
                </p>
              </div>
              <button
                onClick={() => setShowCreateModal(true)}
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
              >
                {t('apiKeys.createKey')}
              </button>
            </div>
          </div>

          <div className="p-6">
            {/* 使用说明 */}
            <div className="bg-blue-50 border border-blue-200 rounded-md p-4 mb-6">
              <h3 className="text-lg font-medium text-blue-900 mb-2">{t('apiKeys.usage')}</h3>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• {t('apiKeys.usageInstructions.1')}</li>
                <li>• {t('apiKeys.usageInstructions.2')}</li>
                <li>• {t('apiKeys.usageInstructions.3')}</li>
                <li>• {t('apiKeys.currentBalance', { credits: user?.credits || 0 })}</li>
              </ul>
            </div>

            {/* 显示新创建的API Key */}
            {createdKey && (
              <div className="bg-green-50 border border-green-200 rounded-md p-4 mb-6">
                <h3 className="text-lg font-medium text-green-900 mb-2">{t('apiKeys.keyCreated')}</h3>
                <p className="text-sm text-green-800 mb-2">
                  {t('apiKeys.saveKeyNotice')}
                </p>
                <div className="flex items-center gap-2">
                  <input
                    type="text"
                    value={createdKey}
                    readOnly
                    className="flex-1 px-3 py-2 border border-green-300 rounded-md bg-white font-mono text-sm"
                  />
                  <button
                    onClick={() => copyToClipboard(createdKey)}
                    className="px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                  >
                    {t('apiKeys.copy')}
                  </button>
                  <button
                    onClick={() => setCreatedKey(null)}
                    className="px-3 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
                  >
                    {t('apiKeys.close')}
                  </button>
                </div>
              </div>
            )}

            {/* API Keys 列表 */}
            {loading ? (
              <div className="text-center py-8">
                <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <p className="mt-2 text-gray-600">{t('common.loading')}</p>
              </div>
            ) : apiKeys.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-600">{t('apiKeys.noKeysFound')}</p>
              </div>
            ) : (
              <div className="space-y-4">
                {apiKeys.map((key) => (
                  <div key={key.id} className="border border-gray-200 rounded-md p-4">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <h3 className="text-lg font-medium text-gray-900">{key.name}</h3>
                        <p className="text-sm text-gray-600 mt-1 font-mono">{key.prefix}</p>
                        {key.description && (
                          <p className="text-sm text-gray-500 mt-1">{key.description}</p>
                        )}
                        <div className="mt-2 text-xs text-gray-500 space-x-4">
                          <span>{t('apiKeys.usageCount')}: {key.usage_count}</span>
                          <span>{t('apiKeys.createdAt')}: {new Date(key.created_at).toLocaleString()}</span>
                          {key.last_used_at && (
                            <span>{t('apiKeys.lastUsed')}: {new Date(key.last_used_at).toLocaleString()}</span>
                          )}
                        </div>
                      </div>
                      <button
                        onClick={() => deleteAPIKey(key.id)}
                        className="text-red-600 hover:text-red-800 text-sm font-medium"
                      >
                        {t('apiKeys.delete')}
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* 创建API Key模态框 */}
        {showCreateModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md">
              <h2 className="text-xl font-bold text-gray-900 mb-4">{t('apiKeys.createNewKey')}</h2>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('apiKeys.keyName')} *
                  </label>
                  <input
                    type="text"
                    value={newKey.name}
                    onChange={(e) => setNewKey({ ...newKey, name: e.target.value })}
                    placeholder={t('apiKeys.keyNamePlaceholder')}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('apiKeys.keyDescription')}
                  </label>
                  <textarea
                    value={newKey.description}
                    onChange={(e) => setNewKey({ ...newKey, description: e.target.value })}
                    placeholder={t('apiKeys.keyDescriptionPlaceholder')}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>

              <div className="flex justify-end gap-3 mt-6">
                <button
                  onClick={() => setShowCreateModal(false)}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800"
                >
                  {t('apiKeys.cancel')}
                </button>
                <button
                  onClick={createAPIKey}
                  disabled={!newKey.name.trim()}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {t('apiKeys.create')}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
} 