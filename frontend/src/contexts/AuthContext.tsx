'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import toast from 'react-hot-toast';
import Cookies from 'js-cookie';
import { authAPI } from '@/lib/api';
import type { 
  User, 
  AuthContextType, 
  RegisterRequest,
  AuthResponse 
} from '@/types/auth';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: React.ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  const isAuthenticated = !!user;

  // 初始化认证状态
  useEffect(() => {
    const initAuth = async () => {
      try {
        // 确保只在客户端运行
        if (typeof window === 'undefined') {
          setIsLoading(false);
          return;
        }

        // 添加短暂延迟确保组件已挂载
        await new Promise(resolve => setTimeout(resolve, 100));
        
        const token = localStorage.getItem('token') || Cookies.get('token');
        const userData = localStorage.getItem('user');
        
        if (token && userData) {
          try {
            const parsedUser = JSON.parse(userData);
            setUser(parsedUser);
            
            // 验证token是否有效
            const currentUser = await authAPI.getCurrentUser();
            setUser(currentUser);
            localStorage.setItem('user', JSON.stringify(currentUser));
          } catch (error) {
            // Token无效或解析失败，清除本地数据
            console.error('Auth validation error:', error);
            logout();
          }
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initAuth();
  }, []);

  // 保存用户信息和token
  const saveAuthData = (authData: AuthResponse) => {
    if (typeof window === 'undefined') return;
    
    const { access_token, user: userData } = authData;
    
    // 保存到localStorage
    localStorage.setItem('token', access_token);
    localStorage.setItem('user', JSON.stringify(userData));
    
    // 保存到cookie (用于SSR)
    Cookies.set('token', access_token, { 
      expires: 7, // 7天过期
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax'
    });
    
    setUser(userData);
  };

  // 发送验证码
  const sendVerificationCode = async (email: string, type: 'login' | 'register') => {
    try {
      const response = await authAPI.sendVerificationCode({ email, type });
      if (response.success) {
        toast.success(response.message);
      } else {
        toast.error(response.message);
      }
    } catch (error: any) {
      const message = error.response?.data?.detail || '发送验证码失败';
      toast.error(message);
      throw error;
    }
  };

  // 邮箱验证码登录
  const login = async (email: string, code: string) => {
    try {
      setIsLoading(true);
      const response = await authAPI.login({ email, verification_code: code });
      saveAuthData(response);
      toast.success('登录成功！');
      router.push('/');
    } catch (error: any) {
      const message = error.response?.data?.detail || '登录失败';
      toast.error(message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // 邮箱验证码注册
  const register = async (data: RegisterRequest) => {
    try {
      setIsLoading(true);
      const response = await authAPI.register(data);
      saveAuthData(response);
      toast.success('注册成功！');
      router.push('/');
    } catch (error: any) {
      const message = error.response?.data?.detail || '注册失败';
      toast.error(message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // 谷歌登录
  const loginWithGoogle = async (credential: string) => {
    try {
      setIsLoading(true);
      const response = await authAPI.googleLogin(credential);
      saveAuthData(response);
      toast.success('登录成功！');
      router.push('/');
    } catch (error: any) {
      const message = error.response?.data?.detail || '谷歌登录失败';
      toast.error(message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // 登出
  const logout = () => {
    // 确保只在客户端执行
    if (typeof window === 'undefined') return;
    
    try {
      // 调用后端登出接口
      authAPI.logout().catch(console.error);
    } catch (error) {
      console.error('Logout API error:', error);
    } finally {
      // 清除本地数据
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      Cookies.remove('token');
      setUser(null);
      router.push('/');
      toast.success('已退出登录');
    }
  };

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated,
    login,
    register,
    logout,
    sendVerificationCode,
    loginWithGoogle,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
} 