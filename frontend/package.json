{"name": "aitools-frontend", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@google-cloud/functions-framework": "^4.0.0", "@headlessui/react": "^2.2.4", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^3.3.2", "@next/font": "14.0.3", "@types/google.accounts": "^0.0.16", "@types/js-cookie": "^3.0.6", "autoprefixer": "^10.4.16", "axios": "^1.6.2", "clsx": "^2.0.0", "framer-motion": "^10.16.16", "google-auth-library": "^9.15.1", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.1.0", "i18next-resources-to-backend": "^1.2.1", "js-cookie": "^3.0.5", "lucide-react": "^0.511.0", "next": "^15.3.2", "next-i18next": "^15.4.2", "postcss": "^8.4.32", "react": "^19.1.0", "react-dom": "^19.1.0", "react-google-recaptcha": "^3.1.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-i18next": "^15.5.2", "react-markdown": "^10.1.0", "rehype-highlight": "^7.0.2", "remark-gfm": "^4.0.1", "swr": "^2.2.4", "tailwindcss": "^3.3.6", "zod": "^3.22.4"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@types/node": "^20.9.4", "@types/react": "^18.2.38", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "eslint-config-next": "14.0.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "prettier": "^3.1.0", "prettier-plugin-tailwindcss": "^0.5.7", "typescript": "^5.3.2"}}