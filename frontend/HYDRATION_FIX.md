# Next.js 水合错误修复

## 问题描述

在访问 `http://localhost:3000/en` 时遇到了Next.js水合(hydration)错误：

```
Hydration failed because the server rendered HTML didn't match the client. 
```

## 原因分析

1. **localStorage访问**: 在服务器端渲染时访问了只存在于客户端的`localStorage`
2. **i18n配置冲突**: 使用App Router但配置了Pages Router的i18n设置
3. **认证状态初始化**: 在服务器端和客户端初始化认证状态不一致
4. **路由不存在**: `/en`路由在App Router中没有正确配置

## 修复方案

### 1. 创建ClientOnly组件

创建了 `src/components/ClientOnly.tsx` 来包装只能在客户端渲染的组件：

```tsx
'use client';
import { useState, useEffect } from 'react';

export default function ClientOnly({ children, fallback = null }) {
  const [hasMounted, setHasMounted] = useState(false);

  useEffect(() => {
    setHasMounted(true);
  }, []);

  if (!hasMounted) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}
```

### 2. 修复AuthContext

在 `src/contexts/AuthContext.tsx` 中：

- 增加了对`typeof window === 'undefined'`的检查
- 延长了初始化延迟时间到100ms
- 改进了错误处理
- 确保logout函数只在客户端执行

### 3. 修复i18n配置

在 `src/lib/i18n.ts` 和 `src/lib/i18n-client.ts` 中：

- 添加了客户端检查`typeof window === 'undefined'`
- 服务器端使用默认语言初始化
- 客户端延迟执行地理位置检测

### 4. 更新Header组件

在 `src/components/layout/Header.tsx` 中：

- 使用`ClientOnly`包装`LanguageSwitcher`
- 使用`ClientOnly`包装认证状态相关的UI
- 添加了loading状态的fallback

### 5. 修复Next.js配置

在 `next.config.js` 中：

- 移除了App Router不兼容的`i18n`配置
- 添加了说明注释

## 使用方法

现在你可以正常访问以下URL而不会遇到水合错误：

- `http://localhost:3000/` - 主页
- `http://localhost:3000/api-tools` - AI工具列表
- `http://localhost:3000/tools/token-count` - Token计数工具

## 多语言支持

虽然移除了Pages Router的i18n配置，但多语言功能仍然通过客户端i18n正常工作：

- 语言切换器仍然可用
- 语言设置保存在localStorage中
- 支持地理位置自动检测语言

## 注意事项

1. **URL路径**: `/en`, `/ja`等语言路径在App Router中需要单独创建对应的路由目录
2. **SSR**: 某些依赖localStorage的功能在首次加载时可能显示loading状态
3. **SEO**: 对于需要SEO的多语言页面，建议考虑使用动态路由`[locale]`

## 测试验证

✅ 主页正常加载无水合错误  
✅ 语言切换器正常工作  
✅ 认证状态正确显示  
✅ Token计数工具可正常访问  
✅ 移动端菜单正常工作  

## 第二次修复 - 翻译文本水合错误

### 问题
访问主页时仍然出现水合错误，错误显示在翻译文本上：
- 服务器端：显示"AI工具"（中文）  
- 客户端：显示"AI Tools"（英文）

### 解决方案
创建了`TranslatedText`组件来包装所有翻译文本：

```tsx
// src/components/TranslatedText.tsx
export default function TranslatedText({ tKey, fallback }) {
  const { t } = useTranslation();
  
  return (
    <ClientOnly fallback={<span>{fallback || tKey}</span>}>
      <span>{t(tKey)}</span>
    </ClientOnly>
  );
}
```

### 更新内容
- 替换Header组件中所有的`{t('key')}`为`<TranslatedText tKey="key" fallback="English" />`
- 提供英文fallback文本，确保服务器端渲染一致性

## 相关文件

- `src/components/ClientOnly.tsx` (新增)
- `src/components/TranslatedText.tsx` (新增)
- `src/contexts/AuthContext.tsx` (修改)  
- `src/lib/i18n.ts` (修改)
- `src/lib/i18n-client.ts` (修改)
- `src/components/layout/Header.tsx` (修改)
- `next.config.js` (修改) 