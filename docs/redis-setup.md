# Redis安装和密码配置指南

## 概述

本项目使用Redis作为缓存和会话存储，为了安全考虑，Redis必须配置密码认证。

## Windows环境安装Redis

### 方式一：使用Chocolatey（推荐）

```bash
# 安装Chocolatey（如果未安装）
Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))

# 安装Redis
choco install redis-64
```

### 方式二：手动下载安装

1. 访问 [Redis Windows版本下载页面](https://github.com/microsoftarchive/redis/releases)
2. 下载最新版本的 `Redis-x64-*.msi` 文件
3. 运行安装程序，按默认设置安装
4. 将Redis安装目录添加到系统PATH环境变量

## Redis密码配置

### 使用项目配置文件（推荐）

1. **修改Redis配置文件**
   
   编辑项目根目录的 `redis.conf` 文件：
   ```conf
   # 修改密码（请使用强密码）
   requirepass your-strong-redis-password
   ```

2. **启动Redis服务**
   ```bash
   # 使用项目配置文件启动
   redis-server redis.conf
   
   # 或使用启动脚本
   start-redis.bat
   ```

### 手动配置密码

```bash
# 启动Redis并设置密码
redis-server --requirepass your-strong-redis-password

# 或启动后通过命令行设置
redis-cli
127.0.0.1:6379> CONFIG SET requirepass "your-strong-redis-password"
127.0.0.1:6379> AUTH your-strong-redis-password
```

## 环境变量配置

在 `.env` 文件中配置Redis连接URL：

```env
# Redis配置（带密码认证）
REDIS_URL=redis://:your-strong-redis-password@localhost:6379/0
```

**URL格式说明：**
- `redis://` - 协议
- `:password` - 密码（注意冒号前缀）
- `@localhost:6379` - 主机和端口
- `/0` - 数据库编号

## 验证Redis连接

### 使用redis-cli测试

```bash
# 连接Redis
redis-cli -h localhost -p 6379 -a your-strong-redis-password

# 测试连接
127.0.0.1:6379> ping
PONG

# 设置和获取测试数据
127.0.0.1:6379> set test "hello"
OK
127.0.0.1:6379> get test
"hello"
```

### 使用Python测试

```python
import redis

# 连接Redis
r = redis.from_url("redis://:your-strong-redis-password@localhost:6379/0")

# 测试连接
try:
    r.ping()
    print("Redis连接成功！")
except redis.ConnectionError:
    print("Redis连接失败！")
```

## 安全建议

1. **使用强密码**
   - 至少12位字符
   - 包含大小写字母、数字和特殊字符
   - 避免使用常见密码

2. **网络安全**
   - 生产环境中绑定内网IP：`bind *************`
   - 使用防火墙限制访问端口6379

3. **禁用危险命令**
   ```conf
   # 在redis.conf中禁用危险命令
   rename-command FLUSHDB ""
   rename-command FLUSHALL ""
   rename-command KEYS ""
   rename-command CONFIG ""
   ```

4. **定期备份**
   ```bash
   # 手动备份
   redis-cli -a your-password --rdb backup.rdb
   
   # 自动备份（在redis.conf中配置）
   save 900 1
   save 300 10
   save 60 10000
   ```

## 常见问题

### Q: Redis启动失败，提示端口被占用
A: 检查是否已有Redis实例在运行：
```bash
tasklist | findstr redis
netstat -an | findstr 6379
```

### Q: 连接Redis时提示认证失败
A: 检查密码是否正确，确保URL格式正确：
```env
# 正确格式
REDIS_URL=redis://:password@localhost:6379/0

# 错误格式
REDIS_URL=redis://password@localhost:6379/0
```

### Q: 应用启动时Redis连接超时
A: 检查Redis服务是否正在运行，防火墙是否阻止连接。

## 生产环境部署

### Docker部署

```yaml
# docker-compose.yml
version: '3.8'
services:
  redis:
    image: redis:7-alpine
    command: redis-server --requirepass ${REDIS_PASSWORD}
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    environment:
      - REDIS_PASSWORD=your-production-password

volumes:
  redis_data:
```

### 云服务

推荐使用云Redis服务：
- **阿里云Redis**：提供高可用和自动备份
- **腾讯云Redis**：支持集群模式
- **AWS ElastiCache**：全托管Redis服务

## 监控和维护

### 监控Redis状态

```bash
# 查看Redis信息
redis-cli -a password info

# 监控实时命令
redis-cli -a password monitor

# 查看慢查询
redis-cli -a password slowlog get 10
```

### 性能优化

1. **内存优化**
   ```conf
   maxmemory 256mb
   maxmemory-policy allkeys-lru
   ```

2. **持久化配置**
   ```conf
   # RDB快照
   save 900 1
   save 300 10
   save 60 10000
   
   # AOF日志
   appendonly yes
   appendfsync everysec
   ```

3. **连接池配置**
   ```python
   # 在Python中使用连接池
   import redis
   
   pool = redis.ConnectionPool(
       host='localhost',
       port=6379,
       password='your-password',
       max_connections=20
   )
   r = redis.Redis(connection_pool=pool)
   ``` 