@echo off
echo ========================================
echo 停止AI工具API平台服务
echo ========================================

echo 正在停止后端服务 (端口8000)...
for /f "tokens=5" %%a in ('netstat -aon ^| find ":8000" ^| find "LISTENING"') do (
    echo 终止进程 %%a
    taskkill /f /pid %%a >nul 2>&1
)

echo 正在停止前端服务 (端口3000)...
for /f "tokens=5" %%a in ('netstat -aon ^| find ":3000" ^| find "LISTENING"') do (
    echo 终止进程 %%a
    taskkill /f /pid %%a >nul 2>&1
)

echo.
echo 服务已停止！
echo ========================================
pause 