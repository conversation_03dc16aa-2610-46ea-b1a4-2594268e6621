---
description: 
globs: 
alwaysApply: false
---
# 测试策略和规范

## 🚨 强制性测试驱动开发 (TDD) 规范

### 核心原则
**所有新功能必须先写测试，再写实现代码！**

### TDD开发流程 (红-绿-重构)
1. **红色阶段**: 先写失败的测试用例
2. **绿色阶段**: 编写最少代码让测试通过
3. **重构阶段**: 优化代码结构，保持测试通过

### 强制性检查点
在每次代码提交前，必须确保：
- ✅ 新功能有对应的测试用例
- ✅ 所有测试用例都能通过
- ✅ 测试覆盖率不低于80%
- ✅ 没有跳过的测试用例 (skip/todo)

## 测试原则
项目采用测试驱动开发 (TDD) 方法，确保每个新功能都有完整的测试覆盖。

## 测试层级

### 1. 单元测试 (Unit Tests)
**目标覆盖率**: 80%以上

#### 前端单元测试
- **工具**: Jest + React Testing Library
- **位置**: [frontend/__tests__/](mdc:frontend/__tests__)
- **覆盖范围**:
  - 组件渲染测试
  - 用户交互测试
  - Hook功能测试
  - 工具函数测试

```typescript
// 示例: 组件测试
describe('ApiToolCard', () => {
  it('should render tool information correctly', () => {
    // 测试组件正确渲染
  });
  
  it('should handle API call on button click', () => {
    // 测试用户交互
  });
});
```

#### 后端单元测试
- **工具**: pytest + pytest-asyncio
- **位置**: [backend/tests/](mdc:backend/tests)
- **覆盖范围**:
  - API端点测试
  - 业务逻辑测试
  - 数据模型测试
  - 工具函数测试

```python
# 示例: API测试
@pytest.mark.asyncio
async def test_create_user():
    # 测试用户创建API
    pass

def test_credit_calculation():
    # 测试积分计算逻辑
    pass
```

### 2. 集成测试 (Integration Tests)
**目标**: 覆盖主要业务流程

#### 前端集成测试
- 页面间导航测试
- API调用集成测试
- 状态管理集成测试

#### 后端集成测试
- 数据库操作测试
- 外部API集成测试
- 认证流程测试

### 3. 端到端测试 (E2E Tests)
**工具**: Playwright
**位置**: [tests/e2e/](mdc:tests/e2e)

#### 关键用户流程
- 用户注册和登录流程
- API工具使用完整流程
- 积分消费和充值流程
- 博客文章阅读流程

```typescript
// 示例: E2E测试
test('complete API usage flow', async ({ page }) => {
  // 1. 用户登录
  // 2. 选择API工具
  // 3. 输入参数并调用
  // 4. 查看结果
  // 5. 检查积分扣除
});
```

## 测试数据管理

### 1. 测试数据库
- 使用独立的测试数据库
- 每个测试用例前后清理数据
- 使用Factory模式创建测试数据

### 2. Mock和Stub
- Mock外部API调用
- Stub复杂的第三方服务
- 使用固定的测试数据集

### 3. 测试环境配置
```python
# backend/tests/conftest.py
@pytest.fixture
def test_db():
    # 创建测试数据库连接
    pass

@pytest.fixture
def test_user():
    # 创建测试用户
    pass
```

## 性能测试

### 1. API性能测试
- 使用Locust进行负载测试
- 测试API响应时间
- 测试并发处理能力

### 2. 前端性能测试
- 使用Lighthouse进行性能审计
- 测试页面加载时间
- 测试交互响应时间

## 安全测试

### 1. 认证授权测试
- 测试未授权访问
- 测试权限边界
- 测试Token安全性

### 2. 输入验证测试
- SQL注入测试
- XSS攻击测试
- CSRF攻击测试

## 测试自动化

### 1. CI/CD集成
- 代码提交时自动运行单元测试
- PR合并前运行完整测试套件
- 部署前运行E2E测试

### 2. 测试报告
- 生成测试覆盖率报告
- 生成性能测试报告
- 集成到项目仪表板

## 测试最佳实践

### 1. 测试命名规范
```python
def test_should_return_error_when_invalid_api_key():
    # 测试名称应该清楚描述测试场景
    pass
```

### 2. 测试独立性
- 每个测试用例应该独立运行
- 不依赖其他测试的执行结果
- 使用setup和teardown清理状态

### 3. 测试可维护性
- 使用Page Object模式 (E2E测试)
- 提取公共测试工具函数
- 保持测试代码简洁明了

### 4. 测试数据
- 使用有意义的测试数据
- 避免硬编码测试值
- 使用常量定义测试配置

## 新功能测试检查清单

在添加新功能时，必须完成以下测试：

### ✅ 前端功能
- [ ] 组件渲染测试
- [ ] 用户交互测试
- [ ] 错误状态测试
- [ ] 加载状态测试
- [ ] 响应式设计测试

### ✅ 后端功能
- [ ] API端点测试
- [ ] 业务逻辑测试
- [ ] 数据验证测试
- [ ] 错误处理测试
- [ ] 权限控制测试

### ✅ 集成测试
- [ ] 前后端集成测试
- [ ] 数据库集成测试
- [ ] 第三方服务集成测试

### ✅ E2E测试
- [ ] 完整用户流程测试
- [ ] 跨浏览器兼容性测试
- [ ] 移动端适配测试
