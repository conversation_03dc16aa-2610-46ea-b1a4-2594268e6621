---
description: 
globs: 
alwaysApply: false
---
# AI工具API网站项目概览

## 项目简介
这是一个AI工具API聚合网站，提供多种AI相关工具的API服务，用户可以在线使用并查看结果。

## 技术栈
- **前端**: Next.js (React框架)
- **后端**: Python FastAPI
- **数据库**: 待定 (建议PostgreSQL + Redis)
- **部署**: 待定 (建议Docker + Kubernetes)

## 主要功能模块

### 1. API工具展示与使用
- 在线API工具列表
- 实时API调用和结果展示
- API文档和使用示例
- API性能监控

### 2. 价格展示 (Price)
- 不同API工具的定价策略
- 套餐对比
- 计费说明

### 3. 博客系统 (Blog)
- API使用教程
- 技术文章
- 更新日志
- 最佳实践分享

### 4. 用户账户系统 (Account)
- 用户注册/登录
- 积分管理
- 使用历史
- API密钥管理
- 账单查看

## 项目结构
```
aitools/
├── frontend/          # Next.js前端应用
├── backend/           # FastAPI后端服务
├── tests/            # 测试用例
├── docs/             # 项目文档
└── docker/           # Docker配置
```

## 开发原则
1. **测试驱动开发**: 每个新功能都必须有对应的测试用例
2. **API优先**: 后端API设计优先，前端基于API开发
3. **模块化设计**: 每个功能模块独立开发和测试
4. **性能优化**: 关注API响应时间和用户体验
5. **安全性**: 实现完善的认证授权机制

