---
description: 
globs: 
alwaysApply: false
---
# 开发工作流程规范

## 🚨 强制性测试驱动开发 (TDD) 流程

### 核心原则
**任何新功能开发都必须遵循TDD流程：测试先行，代码后行！**

### TDD开发步骤 (强制执行)
1. **🔴 红色阶段**: 编写失败的测试用例
   - 明确功能需求和验收标准
   - 编写描述预期行为的测试
   - 确保测试运行失败（因为功能尚未实现）

2. **🟢 绿色阶段**: 编写最少代码让测试通过
   - 只实现让测试通过的最少代码
   - 不考虑代码优雅性，专注于功能实现
   - 确保所有测试通过

3. **🔄 重构阶段**: 优化代码结构
   - 改进代码质量和结构
   - 保持所有测试通过
   - 添加必要的文档和注释

### 违规检查 (自动化检查)
- ❌ **禁止提交**: 没有对应测试的新功能代码
- ❌ **禁止提交**: 测试覆盖率低于80%的代码
- ❌ **禁止提交**: 包含跳过测试(skip/todo)的代码
- ❌ **禁止提交**: 测试失败的代码

### 代码审查强制检查点
- [ ] 是否先写了测试再写实现？
- [ ] 测试用例是否覆盖主要场景？
- [ ] 是否有边界条件和错误处理测试？
- [ ] 测试命名是否清晰描述了预期行为？
- [ ] 代码是否通过了所有测试？

## Git工作流

### 分支策略
采用Git Flow分支模型：

- **main**: 生产环境分支，只接受来自release和hotfix的合并
- **develop**: 开发主分支，集成所有功能分支
- **feature/***: 功能开发分支，从develop分出，完成后合并回develop
- **release/***: 发布准备分支，从develop分出，完成后合并到main和develop
- **hotfix/***: 紧急修复分支，从main分出，完成后合并到main和develop

### 分支命名规范
```
feature/api-tools-management
feature/user-authentication
bugfix/login-validation-error
hotfix/security-vulnerability
release/v1.0.0
```

### 提交消息规范
使用Conventional Commits格式：

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

**类型 (type)**:
- `feat`: 新功能
- `fix`: 错误修复
- `docs`: 文档更新
- `style`: 代码格式化
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

**示例**:
```
feat(api): add user authentication endpoint
fix(frontend): resolve login form validation issue
docs(readme): update installation instructions
test(backend): add unit tests for billing service
```

## 开发流程

### 1. 功能开发流程

#### 步骤1: 创建功能分支
```bash
git checkout develop
git pull origin develop
git checkout -b feature/new-api-tool
```

#### 步骤2: TDD开发流程 (强制执行)
1. **🔴 编写失败测试**: 先写测试用例，确保测试失败
2. **🟢 实现功能代码**: 编写最少代码让测试通过
3. **🔄 重构优化**: 优化代码结构，保持测试通过
4. **✅ 质量检查**: 运行代码质量检查和覆盖率检查

**注意**: 违反TDD流程的代码将被拒绝合并！

#### 步骤3: 提交代码
```bash
git add .
git commit -m "feat(api): add new AI text generation tool"
git push origin feature/new-api-tool
```

#### 步骤4: 创建Pull Request
- 填写详细的PR描述
- 关联相关的Issue
- 请求代码审查
- 确保CI/CD检查通过

#### 步骤5: 代码审查和合并
- 至少需要一个团队成员的审查
- 解决所有审查意见
- 合并到develop分支

### 2. 发布流程

#### 步骤1: 创建发布分支
```bash
git checkout develop
git pull origin develop
git checkout -b release/v1.1.0
```

#### 步骤2: 发布准备
1. 更新版本号
2. 更新CHANGELOG.md
3. 运行完整测试套件
4. 修复发现的问题

#### 步骤3: 合并和标签
```bash
git checkout main
git merge release/v1.1.0
git tag v1.1.0
git checkout develop
git merge release/v1.1.0
```

## 代码质量标准

### 1. 代码审查检查清单

#### 前端代码审查
- [ ] 组件设计是否合理
- [ ] TypeScript类型定义是否完整
- [ ] 是否遵循React最佳实践
- [ ] 样式是否响应式
- [ ] 是否有适当的错误处理
- [ ] 是否有对应的测试用例

#### 后端代码审查
- [ ] API设计是否RESTful
- [ ] 数据验证是否完整
- [ ] 错误处理是否恰当
- [ ] 数据库操作是否优化
- [ ] 安全性考虑是否充分
- [ ] 是否有对应的测试用例

### 2. 自动化检查

#### 前端检查
```json
// package.json scripts
{
  "lint": "eslint . --ext .ts,.tsx",
  "type-check": "tsc --noEmit",
  "test": "jest",
  "test:coverage": "jest --coverage"
}
```

#### 后端检查
```python
# 代码质量工具
black --check .          # 代码格式化检查
flake8 .                 # 代码风格检查
mypy .                   # 类型检查
pytest --cov=app        # 测试覆盖率
```

## 环境管理

### 1. 开发环境
- 本地开发数据库
- 热重载开发服务器
- 调试工具配置

### 2. 测试环境
- 独立的测试数据库
- 模拟外部服务
- 自动化测试执行

### 3. 预生产环境
- 生产数据的副本
- 完整的集成测试
- 性能测试

### 4. 生产环境
- 高可用配置
- 监控和日志
- 备份策略

## CI/CD流程

### 1. 持续集成 (CI)
```yaml
# .github/workflows/ci.yml
name: CI
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Node.js
        uses: actions/setup-node@v2
      - name: Install dependencies
        run: npm install
      - name: Run tests
        run: npm test
      - name: Run linting
        run: npm run lint
```

### 2. 持续部署 (CD)
- 自动部署到测试环境
- 手动审批部署到生产环境
- 回滚机制

## 文档要求

### 1. 代码文档
- 函数和类的文档注释
- 复杂逻辑的内联注释
- API文档自动生成

### 2. 项目文档
- README.md: 项目介绍和快速开始
- CONTRIBUTING.md: 贡献指南
- CHANGELOG.md: 版本更新记录
- API.md: API使用文档

### 3. 架构文档
- 系统架构图
- 数据库设计文档
- 部署架构文档

## 性能监控

### 1. 前端性能
- 页面加载时间监控
- 用户交互响应时间
- 错误率统计

### 2. 后端性能
- API响应时间监控
- 数据库查询性能
- 系统资源使用率

### 3. 业务指标
- API调用量统计
- 用户活跃度
- 收入指标

## 安全规范

### 1. 代码安全
- 依赖包安全扫描
- 静态代码安全分析
- 敏感信息检查

### 2. 部署安全
- HTTPS强制使用
- 安全头配置
- 访问控制

### 3. 数据安全
- 数据加密
- 备份策略
- 访问日志
