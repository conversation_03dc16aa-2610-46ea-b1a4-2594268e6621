# AI Tools 认证系统设置指南

## 功能特性

- ✅ 邮箱验证码登录/注册
- ✅ 谷歌OAuth登录/注册
- ✅ JWT令牌认证
- ✅ 用户状态管理
- ✅ 响应式UI设计
- ✅ 移动端适配

## 前端设置

### 1. 安装依赖

```bash
cd frontend
npm install --legacy-peer-deps
```

### 2. 环境配置

创建 `frontend/.env.local` 文件：

```env
# API配置
NEXT_PUBLIC_API_URL=http://localhost:8000

# 谷歌OAuth配置
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your-google-client-id-here

# 其他配置
NEXT_PUBLIC_APP_NAME=AI Tools
NEXT_PUBLIC_APP_VERSION=1.0.0
```

### 3. 谷歌OAuth设置

1. 前往 [Google Cloud Console](https://console.cloud.google.com/)
2. 创建新项目或选择现有项目
3. 启用 Google+ API 和 Google Identity 服务
4. 创建 OAuth 2.0 客户端 ID
5. 添加授权的重定向 URI：
   - `http://localhost:3000`
   - `https://yourdomain.com` (生产环境)
6. 将客户端 ID 添加到环境变量中

## 后端设置

### 1. 安装依赖

```bash
cd backend
pip install -r requirements.txt
```

### 2. 环境配置

更新 `backend/.env` 文件：

```env
# JWT配置
JWT_SECRET_KEY=your-super-secret-jwt-key-here
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 谷歌OAuth配置
GOOGLE_CLIENT_ID=your-google-client-id-here
GOOGLE_CLIENT_SECRET=your-google-client-secret-here

# 邮件配置（可选）
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_TLS=true
```

## 使用说明

### 登录页面

访问 `/auth/login` 进入登录页面，支持：

1. **邮箱验证码登录**：
   - 输入邮箱地址
   - 点击"发送验证码"
   - 输入收到的6位验证码
   - 点击"登录"

2. **谷歌登录**：
   - 点击"使用 Google 登录"按钮
   - 选择谷歌账户并授权

### 注册页面

访问 `/auth/register` 进入注册页面，支持：

1. **邮箱验证码注册**：
   - 输入邮箱地址
   - 输入用户名（3-20个字符）
   - 输入真实姓名（可选）
   - 点击"发送验证码"
   - 输入收到的6位验证码
   - 点击"创建账户"

2. **谷歌注册**：
   - 点击"使用 Google 登录"按钮
   - 如果是新用户，系统会自动创建账户

### 用户状态管理

登录成功后：
- 用户信息存储在 Context 中
- JWT 令牌保存在 localStorage 和 cookie 中
- Header 显示用户菜单和积分信息
- 支持自动刷新令牌

## API 接口

### 认证相关接口

- `POST /api/v1/auth/send-code` - 发送验证码
- `POST /api/v1/auth/login` - 验证码登录
- `POST /api/v1/auth/register` - 验证码注册
- `POST /api/v1/auth/google` - 谷歌OAuth登录
- `GET /api/v1/auth/me` - 获取当前用户信息
- `POST /api/v1/auth/refresh` - 刷新令牌
- `POST /api/v1/auth/logout` - 登出

### 请求示例

#### 发送验证码
```bash
curl -X POST "http://localhost:8000/api/v1/auth/send-code" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "type": "login"
  }'
```

#### 验证码登录
```bash
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "verification_code": "123456"
  }'
```

## 技术栈

### 前端
- Next.js 15
- React 19
- TypeScript
- Tailwind CSS
- React Hook Form + Zod
- Headless UI
- React Hot Toast
- js-cookie

### 后端
- FastAPI
- SQLAlchemy
- JWT (python-jose)
- Passlib (bcrypt)
- Google Auth Library
- Pydantic

## 安全特性

1. **密码安全**：使用 bcrypt 哈希加密
2. **JWT 令牌**：包含过期时间和用户信息
3. **验证码**：5分钟过期，使用后自动删除
4. **谷歌OAuth**：官方认证库验证令牌
5. **CORS 配置**：限制跨域访问
6. **输入验证**：前后端双重验证

## 注意事项

1. **验证码发送**：当前使用模拟发送，生产环境需要集成真实邮件服务
2. **验证码存储**：当前存储在内存中，生产环境建议使用 Redis
3. **环境变量**：生产环境请使用安全的密钥和配置
4. **HTTPS**：生产环境必须使用 HTTPS
5. **谷歌客户端 ID**：需要在 Google Cloud Console 中正确配置

## 故障排除

### 谷歌登录问题
1. 检查客户端 ID 是否正确
2. 确认重定向 URI 已添加到谷歌控制台
3. 检查浏览器控制台是否有错误信息

### 验证码问题
1. 检查后端日志中的验证码输出
2. 确认验证码未过期（5分钟有效期）
3. 检查邮箱地址是否正确

### 令牌问题
1. 检查 JWT_SECRET_KEY 是否一致
2. 确认令牌未过期
3. 检查 Authorization 请求头格式

## 开发和生产部署

### 开发环境
```bash
# 启动后端
cd backend
uvicorn app.main:app --reload --port 8000

# 启动前端
cd frontend
npm run dev
```

### 生产环境
1. 使用安全的环境变量
2. 启用 HTTPS
3. 配置反向代理
4. 使用生产级数据库
5. 设置监控和日志 