#!/bin/bash

# 脚本用途: 启动 AI 工具 API 平台 (后端和前端服务)

echo "启动AI工具API平台..."

echo "启动后端服务..."
# 进入后端目录
cd backend || { echo "错误: 无法进入 backend 目录"; exit 1; }

# 启动 uvicorn 后端服务，并在后台运行
# 使用 nohup 可以让进程在终端关闭后继续运行，并将输出重定向到 nohup.out
# 如果你不需要 nohup 的特性，可以直接使用:
# uvicorn app.main:app --reload --host 0.0.0.0 --port 8000 &
echo "正在启动 uvicorn 后端服务 (端口 8000)..."
nohup uvicorn app.main:app --reload --host 0.0.0.0 --port 8000 > uvicorn.log 2>&1 &

# 获取后台进程的PID (可选, 用于后续管理)
BACKEND_PID=$!
echo "后端服务已启动 (PID: $BACKEND_PID)。日志请查看 backend/uvicorn.log 或 backend/nohup.out (如果未使用 > uvicorn.log)"

echo "等待后端启动 (3秒)..."
sleep 3 # 等待3秒

echo "启动前端服务..."
# 返回上级目录再进入前端目录
cd ../frontend || { echo "错误: 无法进入 frontend 目录"; exit 1; }

# 启动 npm 前端开发服务
# npm run dev 通常会占据当前终端，直到你手动停止它 (例如按 Ctrl+C)
# 这与原始 .bat 脚本中最后一个命令的行为一致
echo "正在启动 npm 前端开发服务 (通常在 http://localhost:3000 或类似地址)..."
npm run dev

echo "脚本执行完毕。"
echo "如果前端服务仍在前台运行，您可能需要手动停止它 (例如按 Ctrl+C 来停止 npm run dev)。"
echo "后端服务 (uvicorn) 应该仍在后台运行。您可以使用 'kill $BACKEND_PID' 或 'pkill -f uvicorn' 来停止它。"