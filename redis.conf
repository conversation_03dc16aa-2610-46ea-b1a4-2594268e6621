# Redis配置文件
# 端口配置
port 6379

# 绑定地址 (生产环境建议只绑定内网IP)
bind 127.0.0.1

# 密码认证 (请修改为强密码)
requirepass your-redis-password

# 数据库数量
databases 16

# 持久化配置
save 900 1
save 300 10
save 60 10000

# RDB文件名
dbfilename dump.rdb

# 工作目录
dir ./

# 日志级别
loglevel notice

# 日志文件
logfile "redis.log"

# 最大内存限制 (根据服务器配置调整)
maxmemory 256mb

# 内存淘汰策略
maxmemory-policy allkeys-lru

# 禁用危险命令
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command KEYS ""
rename-command CONFIG ""
rename-command SHUTDOWN SHUTDOWN_REDIS
rename-command DEBUG ""
rename-command EVAL ""

# 客户端连接数限制
maxclients 10000

# 超时设置
timeout 300

# TCP keepalive
tcp-keepalive 300

# 慢查询日志
slowlog-log-slower-than 10000
slowlog-max-len 128 