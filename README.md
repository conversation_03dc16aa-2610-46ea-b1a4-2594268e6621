# AI工具API平台

一个集成多种AI工具的API聚合平台，提供文本美化、语言检测、文本生成图片等功能。

## 技术栈

- **前端**: Next.js 14 + TypeScript + Tailwind CSS
- **后端**: Python FastAPI + SQLAlchemy + MySQL + Redis
- **AI服务**: OpenRouter API + langdetect

## 快速启动

### 方式一：使用启动脚本（推荐）

1. **启动Redis服务（如果需要）**
```bash
# 启动Redis服务（带密码认证）
start-redis.bat
```

2. **启动应用服务**
```bash
# 启动所有服务（在新窗口中运行）
start.bat

# 或使用简化版本（在当前窗口运行）
start-simple.bat

# 停止所有服务
stop.bat
```

### 方式二：手动启动

#### 1. 启动Redis服务

```bash
# 方式一：使用项目提供的配置文件
redis-server redis.conf

# 方式二：手动启动并设置密码
redis-server --requirepass your-redis-password
```

#### 2. 启动后端服务

```bash
cd backend
pip install -r requirements.txt

# 创建MySQL数据库
mysql -u root -p < database_schema.sql

uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

#### 3. 启动前端服务

```bash
cd frontend
npm install
npm run dev
```

## 访问地址

- **前端应用**: http://localhost:3000
- **后端API**: http://localhost:8000
- **API文档**: http://localhost:8000/docs
- **API调试**: http://localhost:8000/redoc

## 环境配置

### 后端环境变量

复制 `backend/.env.example` 为 `backend/.env` 并配置：

```env
# OpenRouter API密钥
OPENROUTER_API_KEY=your-openrouter-api-key-here

# 数据库配置
DATABASE_URL=mysql+pymysql://root:password@localhost:3306/aitools

# Redis配置（带密码认证）
REDIS_URL=redis://:your-redis-password@localhost:6379/0
```

> 📖 **详细的Redis安装和配置指南请参考：[docs/redis-setup.md](docs/redis-setup.md)**

## 功能特性

### AI工具
- **文本美化**: 使用Claude-3-haiku优化文本表达
- **语言检测**: 基于langdetect库检测文本语言
- **文本生成图片**: 使用Flux模型根据文本生成图片

### 平台功能
- 用户积分系统
- API使用统计
- 响应式Web界面
- RESTful API设计

## 项目结构

```
aitools/
├── backend/                 # FastAPI后端
│   ├── app/
│   │   ├── api/            # API路由
│   │   ├── core/           # 核心配置
│   │   ├── models/         # 数据模型
│   │   ├── schemas/        # Pydantic模式
│   │   └── services/       # 业务逻辑
│   └── requirements.txt
├── frontend/               # Next.js前端
│   ├── src/
│   │   ├── app/           # App Router页面
│   │   ├── components/    # React组件
│   │   ├── lib/          # 工具库
│   │   └── types/        # TypeScript类型
│   └── package.json
├── redis.conf             # Redis配置文件
├── start-redis.bat        # Redis启动脚本
├── start.bat              # 启动脚本
├── start-simple.bat       # 简化启动脚本
└── stop.bat              # 停止脚本
```

## 开发说明

### 🚨 强制性测试驱动开发 (TDD) 规范

项目严格遵循TDD开发流程：

1. **🔴 红色阶段**: 先写失败的测试用例
2. **🟢 绿色阶段**: 编写最少代码让测试通过  
3. **🔄 重构阶段**: 优化代码结构，保持测试通过

### 开发规范
- 前端使用TypeScript严格模式
- 后端使用Pydantic进行数据验证
- 所有API都有完整的文档和类型定义
- 包含完整的错误处理和日志记录
- **强制要求**: 每个新功能都必须有对应的测试用例
- **强制要求**: 测试覆盖率不低于80%

### 安装TDD检查工具

```bash
# 安装Git hooks来强制执行TDD规范
install-hooks.bat
```

安装后，每次代码提交都会自动检查：
- ✅ 是否有对应的测试文件
- ✅ 所有测试是否通过
- ✅ 测试覆盖率是否达到80%
- ✅ 是否有跳过的测试用例

## 许可证

MIT License

## 🚀 功能特性

### 已实现的AI工具
- **文本美化** ✨ - 使用AI技术美化和改进文本表达，支持多种风格转换
- **语言检测** 🌐 - 自动检测文本的语言类型，支持多种语言识别  
- **文本生成图片** 🎨 - 根据文本描述生成高质量图片，支持多种艺术风格

### 核心功能
- 🎯 **在线工具使用** - 直观的Web界面，无需编程知识
- 🔌 **RESTful API** - 完整的API接口，支持第三方集成
- 💰 **积分计费系统** - 灵活的计费模式，按使用量付费
- 📊 **使用统计** - 详细的使用数据和分析报告
- 🔐 **用户管理** - 完整的用户注册、登录和权限管理
- 📱 **响应式设计** - 支持桌面端和移动端访问

## 🏗️ 技术架构

### 前端 (Next.js)
- **框架**: Next.js 14 (App Router)
- **样式**: Tailwind CSS
- **状态管理**: React Hooks + SWR
- **类型检查**: TypeScript
- **HTTP客户端**: Axios

### 后端 (FastAPI)
- **框架**: FastAPI
- **数据库**: PostgreSQL + Redis
- **ORM**: SQLAlchemy
- **认证**: JWT
- **AI服务**: OpenRouter API
- **语言检测**: langdetect

## 📦 项目结构

```
aitools/
├── frontend/                 # Next.js前端应用
│   ├── src/
│   │   ├── app/              # App Router页面
│   │   ├── components/       # React组件
│   │   ├── lib/              # 工具函数和API客户端
│   │   ├── types/            # TypeScript类型定义
│   │   └── hooks/            # 自定义Hooks
│   ├── package.json
│   └── tailwind.config.js
├── backend/                  # FastAPI后端服务
│   ├── app/
│   │   ├── api/              # API路由
│   │   ├── core/             # 核心配置
│   │   ├── models/           # 数据模型
│   │   ├── schemas/          # Pydantic模式
│   │   └── services/         # 业务逻辑
│   ├── requirements.txt
│   └── env.example
├── tests/                    # 测试用例
├── docs/                     # 项目文档
└── README.md
```

## 🛠️ 安装和运行

### 环境要求
- Node.js 18+
- Python 3.9+
- MySQL 8.0+ 或 PostgreSQL 13+
- Redis 6+ (需要密码认证)

### 后端设置

1. **安装Redis并配置密码**
```bash
# Windows (使用Chocolatey)
choco install redis-64

# 或下载安装包
# https://github.com/microsoftarchive/redis/releases

# 使用项目配置文件启动Redis
redis-server redis.conf

# 或手动设置密码启动
redis-server --requirepass your-redis-password
```

2. **安装Python依赖**
```bash
cd backend
pip install -r requirements.txt
```

3. **环境配置**
```bash
cp env.example .env
# 编辑 .env 文件，配置数据库、Redis密码和API密钥
```

4. **数据库初始化**
```bash
# 创建数据库表
mysql -u root -p < database_schema.sql
```

5. **启动后端服务**
```bash
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### 前端设置

1. **安装依赖**
```bash
cd frontend
npm install
```

2. **环境配置**
```bash
# 创建 .env.local 文件
echo "NEXT_PUBLIC_API_URL=http://localhost:8000" > .env.local
```

3. **启动前端服务**
```bash
npm run dev
```

### 访问应用
- 前端: http://localhost:3000
- 后端API文档: http://localhost:8000/docs

## 🔧 配置说明

### 后端环境变量
```env
# 应用配置
APP_NAME=AI Tools API
DEBUG=True
SECRET_KEY=your-secret-key

# 数据库
DATABASE_URL=postgresql://user:pass@localhost:5432/aitools
REDIS_URL=redis://localhost:6379/0

# OpenRouter API (必需)
OPENROUTER_API_KEY=your-openrouter-api-key
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1

# JWT认证
JWT_SECRET_KEY=your-jwt-secret
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
```

### 前端环境变量
```env
NEXT_PUBLIC_API_URL=http://localhost:8000
```

## 🧪 测试

### 后端测试
```bash
cd backend
pytest tests/ -v --cov=app
```

### 前端测试
```bash
cd frontend
npm run test
npm run test:coverage
```

## 📚 API文档

启动后端服务后，访问以下地址查看完整的API文档：
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

### 主要API端点

#### 工具管理
- `GET /api/v1/ai-tools/` - 获取工具列表
- `GET /api/v1/ai-tools/categories` - 获取工具分类

#### AI工具调用
- `POST /api/v1/ai-tools/text-beautify` - 文本美化
- `POST /api/v1/ai-tools/language-detect` - 语言检测
- `POST /api/v1/ai-tools/text-to-image` - 文本生成图片

## 🚀 部署

### Docker部署 (推荐)
```bash
# 构建和启动所有服务
docker-compose up -d
```

### 手动部署
1. 配置生产环境的数据库和Redis
2. 设置环境变量
3. 构建前端: `npm run build`
4. 启动后端: `uvicorn app.main:app --host 0.0.0.0 --port 8000`
5. 部署前端到CDN或静态托管服务

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支: `git checkout -b feature/new-tool`
3. 提交更改: `git commit -am 'Add new AI tool'`
4. 推送分支: `git push origin feature/new-tool`
5. 提交Pull Request

### 开发规范
- 遵循现有的代码风格
- 为新功能添加测试用例
- 更新相关文档
- 确保所有测试通过

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🆘 支持

如果您遇到问题或有建议，请：
1. 查看 [FAQ](docs/FAQ.md)
2. 搜索现有的 [Issues](https://github.com/your-repo/aitools/issues)
3. 创建新的 Issue

## 🔮 路线图

### 即将推出的功能
- [ ] 更多AI工具集成
- [ ] 用户仪表板
- [ ] API密钥管理
- [ ] 批量处理功能
- [ ] Webhook支持
- [ ] 多语言支持

### 长期计划
- [ ] 自定义AI模型训练
- [ ] 企业级功能
- [ ] 移动应用
- [ ] 第三方插件系统

---

**AI Tools** - 让AI触手可及 🚀 