#!/bin/sh
# AI工具平台 - TDD强制检查 Pre-commit Hook

echo "🚨 执行TDD强制检查..."

# 检查是否有暂存的文件
if git diff --cached --quiet; then
    echo "❌ 没有暂存的文件，无需检查"
    exit 0
fi

# 获取暂存的文件
STAGED_FILES=$(git diff --cached --name-only)

# 检查前端文件
FRONTEND_FILES=$(echo "$STAGED_FILES" | grep "^frontend/src/" | grep -E "\.(ts|tsx|js|jsx)$" | grep -v "\.test\." | grep -v "\.spec\.")
FRONTEND_TEST_FILES=$(echo "$STAGED_FILES" | grep "^frontend/" | grep -E "\.(test|spec)\.(ts|tsx|js|jsx)$")

# 检查后端文件
BACKEND_FILES=$(echo "$STAGED_FILES" | grep "^backend/app/" | grep "\.py$" | grep -v "test_")
BACKEND_TEST_FILES=$(echo "$STAGED_FILES" | grep "^backend/tests/" | grep "test_.*\.py$")

# 标记是否有违规
VIOLATIONS=0

echo "📋 检查TDD规范..."

# 检查前端文件是否有对应的测试
if [ -n "$FRONTEND_FILES" ]; then
    echo "🔍 检查前端文件的测试覆盖..."
    for file in $FRONTEND_FILES; do
        # 跳过配置文件和类型定义文件
        if echo "$file" | grep -qE "(config|types|\.d\.ts)"; then
            continue
        fi
        
        # 生成可能的测试文件路径
        dir=$(dirname "$file")
        filename=$(basename "$file" | sed 's/\.[^.]*$//')
        
        # 检查多种可能的测试文件位置
        test_file1="${dir}/__tests__/${filename}.test.tsx"
        test_file2="${dir}/__tests__/${filename}.test.ts"
        test_file3="${dir}/${filename}.test.tsx"
        test_file4="${dir}/${filename}.test.ts"
        
        if [ ! -f "$test_file1" ] && [ ! -f "$test_file2" ] && [ ! -f "$test_file3" ] && [ ! -f "$test_file4" ]; then
            echo "❌ 缺少测试文件: $file"
            echo "   期望的测试文件位置: $test_file1 或 $test_file2"
            VIOLATIONS=1
        else
            echo "✅ 找到测试文件: $file"
        fi
    done
fi

# 检查后端文件是否有对应的测试
if [ -n "$BACKEND_FILES" ]; then
    echo "🔍 检查后端文件的测试覆盖..."
    for file in $BACKEND_FILES; do
        # 跳过配置文件和初始化文件
        if echo "$file" | grep -qE "(__init__|config|main)\.py"; then
            continue
        fi
        
        # 生成测试文件路径
        relative_path=$(echo "$file" | sed 's|^backend/app/||')
        test_file="backend/tests/test_${relative_path}"
        
        if [ ! -f "$test_file" ]; then
            echo "❌ 缺少测试文件: $file"
            echo "   期望的测试文件: $test_file"
            VIOLATIONS=1
        else
            echo "✅ 找到测试文件: $file"
        fi
    done
fi

# 运行前端测试
if [ -n "$FRONTEND_TEST_FILES" ] || [ -n "$FRONTEND_FILES" ]; then
    echo "🧪 运行前端测试..."
    cd frontend
    if ! npm test -- --watchAll=false --coverage --coverageThreshold='{"global":{"branches":80,"functions":80,"lines":80,"statements":80}}' --passWithNoTests; then
        echo "❌ 前端测试失败或覆盖率不足80%"
        VIOLATIONS=1
    else
        echo "✅ 前端测试通过"
    fi
    cd ..
fi

# 运行后端测试
if [ -n "$BACKEND_TEST_FILES" ] || [ -n "$BACKEND_FILES" ]; then
    echo "🧪 运行后端测试..."
    cd backend
    if ! python -m pytest --cov=app --cov-fail-under=80 -v; then
        echo "❌ 后端测试失败或覆盖率不足80%"
        VIOLATIONS=1
    else
        echo "✅ 后端测试通过"
    fi
    cd ..
fi

# 检查是否有跳过的测试
echo "🔍 检查跳过的测试..."
SKIPPED_TESTS=$(git diff --cached | grep -E "(skip|todo|xit|xdescribe)" || true)
if [ -n "$SKIPPED_TESTS" ]; then
    echo "❌ 发现跳过的测试用例:"
    echo "$SKIPPED_TESTS"
    echo "请移除所有跳过的测试用例 (skip, todo, xit, xdescribe)"
    VIOLATIONS=1
fi

# 最终检查结果
if [ $VIOLATIONS -eq 1 ]; then
    echo ""
    echo "🚨 TDD规范检查失败！"
    echo ""
    echo "请确保："
    echo "1. 每个新功能文件都有对应的测试文件"
    echo "2. 所有测试都能通过"
    echo "3. 测试覆盖率达到80%以上"
    echo "4. 没有跳过的测试用例"
    echo ""
    echo "TDD开发流程："
    echo "🔴 1. 先写失败的测试"
    echo "🟢 2. 编写代码让测试通过"
    echo "🔄 3. 重构优化代码"
    echo ""
    echo "提交被拒绝！请修复上述问题后重新提交。"
    exit 1
fi

echo ""
echo "✅ TDD规范检查通过！"
echo "🎉 代码符合测试驱动开发要求，可以提交。"
exit 0 