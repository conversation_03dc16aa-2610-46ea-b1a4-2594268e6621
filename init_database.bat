@echo off
echo ========================================
echo 初始化MySQL数据库
echo ========================================

echo 请确保MySQL服务已启动
echo.

set /p mysql_user="请输入MySQL用户名 (默认: root): "
if "%mysql_user%"=="" set mysql_user=root

set /p mysql_password="请输入MySQL密码: "

echo.
echo 正在创建数据库和表结构...
mysql -u %mysql_user% -p%mysql_password% < backend/database_schema.sql

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo 数据库初始化完成！
    echo ========================================
    echo.
    echo 数据库名: aitools
    echo 已创建的表:
    echo - users (用户表)
    echo - api_tools (API工具表)
    echo - api_usage (API使用记录表)
    echo - credit_transactions (积分交易记录表)
    echo - rate_limits (速率限制表)
    echo.
    echo 已插入初始数据:
    echo - 3个AI工具 (文本美化、语言检测、文本生成图片)
    echo - 管理员账户 (用户名: admin, 密码: admin123)
    echo.
) else (
    echo.
    echo ========================================
    echo 数据库初始化失败！
    echo ========================================
    echo 请检查:
    echo 1. MySQL服务是否已启动
    echo 2. 用户名和密码是否正确
    echo 3. 是否有创建数据库的权限
)

echo.
echo 按任意键关闭此窗口...
pause >nul 