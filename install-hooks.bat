@echo off
echo ========================================
echo 安装TDD强制检查Git Hooks
echo ========================================

echo.
echo 正在安装Git pre-commit hook...

:: 检查是否在Git仓库中
if not exist ".git" (
    echo 错误: 当前目录不是Git仓库
    pause
    exit /b 1
)

:: 创建hooks目录（如果不存在）
if not exist ".git\hooks" (
    mkdir ".git\hooks"
)

:: 复制pre-commit hook
copy ".githooks\pre-commit" ".git\hooks\pre-commit" >nul

:: 在Windows上，Git hooks需要是可执行的
:: 我们创建一个批处理版本
echo @echo off > ".git\hooks\pre-commit.bat"
echo bash "%~dp0pre-commit" >> ".git\hooks\pre-commit.bat"

echo.
echo ========================================
echo Git Hooks安装完成！
echo ========================================
echo.
echo 现在每次提交代码时都会自动执行TDD检查：
echo ✅ 检查是否有对应的测试文件
echo ✅ 运行所有测试确保通过
echo ✅ 检查测试覆盖率（需要≥80%%）
echo ✅ 检查是否有跳过的测试用例
echo.
echo 如果检查失败，提交将被拒绝。
echo 这确保了所有代码都遵循TDD开发流程。
echo.

pause 