@echo off
echo ========================================
echo 启动AI工具API平台
echo ========================================

echo.
echo 检查数据库连接...
echo ========================================

:: 检查MySQL是否运行
tasklist /FI "IMAGENAME eq mysqld.exe" 2>NUL | find /I /N "mysqld.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo MySQL服务正在运行 ✓
) else (
    echo 警告: MySQL服务未运行，请先启动MySQL服务
    echo 或运行 init_database.bat 初始化数据库
)

:: 检查Redis是否运行
tasklist /FI "IMAGENAME eq redis-server.exe" 2>NUL | find /I /N "redis-server.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo Redis服务正在运行 ✓
) else (
    echo 警告: Redis服务未运行，请运行 start-redis.bat 启动Redis
    echo 或确保Redis服务已启动并配置了密码认证
)

echo.
echo 正在启动后端服务...
echo ========================================

:: 启动后端服务 (在新窗口中运行)
start "后端服务 - FastAPI" cmd /k "cd /d backend && uvicorn app.main:app --reload --host 0.0.0.0 --port 8000"

:: 等待后端服务启动
echo 等待后端服务启动...
timeout /t 5 /nobreak >nul

echo.
echo 正在启动前端服务...
echo ========================================

:: 启动前端服务 (在新窗口中运行)
start "前端服务 - Next.js" cmd /k "cd /d frontend && npm run dev"

echo.
echo ========================================
echo 服务启动完成！
echo.
echo 后端API: http://localhost:8000
echo API文档: http://localhost:8000/docs
echo 前端应用: http://localhost:3000
echo ========================================
echo.
echo 按任意键关闭此窗口...
pause >nul 