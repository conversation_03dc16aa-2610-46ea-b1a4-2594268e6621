import pymysql
from app.core.config import settings

def check_table_structure():
    # 解析数据库连接信息
    db_url = settings.DATABASE_URL
    # mysql+pymysql://root:$<EMAIL>:25454/aitools
    parts = db_url.replace('mysql+pymysql://', '').split('@')
    user_pass = parts[0].split(':')
    host_port_db = parts[1].split('/')
    host_port = host_port_db[0].split(':')
    
    connection = pymysql.connect(
        host=host_port[0],
        port=int(host_port[1]),
        user=user_pass[0],
        password=user_pass[1],
        database=host_port_db[1],
        charset='utf8mb4'
    )
    
    try:
        with connection.cursor() as cursor:
            # 检查api_tools表结构
            cursor.execute("DESCRIBE api_tools")
            result = cursor.fetchall()
            print("现有api_tools表结构:")
            for row in result:
                print(f"  {row[0]}: {row[1]}")
            
            print("\n检查是否需要添加缺失的列...")
            
            # 获取现有列名
            existing_columns = [row[0] for row in result]
            
            # 需要的列
            required_columns = {
                'slug': 'VARCHAR(100) UNIQUE',
                'icon': 'VARCHAR(10)',
                'total_calls': 'INT DEFAULT 0',
                'success_rate': 'FLOAT DEFAULT 0.0'
            }
            
            # 添加缺失的列
            for col_name, col_def in required_columns.items():
                if col_name not in existing_columns:
                    print(f"添加列 {col_name}...")
                    cursor.execute(f"ALTER TABLE api_tools ADD COLUMN {col_name} {col_def}")
                    connection.commit()
            
            print("表结构更新完成!")
            
    finally:
        connection.close()

if __name__ == "__main__":
    check_table_structure() 