import time
import json
import httpx
import tiktoken
from typing import Dict, Any, Optional
from langdetect import detect, detect_langs
from langdetect.lang_detect_exception import LangDetectException

from ..core.config import settings


class AIService:
    def __init__(self):
        self.openrouter_api_key = settings.OPENROUTER_API_KEY
        self.openrouter_base_url = settings.OPENROUTER_BASE_URL
        # 第三方文本重写API
        self.rewrite_api_url = "http://49.51.230.37:8060/api/rewrite"
        
    async def beautify_text(self, text: str, style: str = "professional") -> Dict[str, Any]:
        """文本美化服务 - 使用第三方API"""
        start_time = time.time()
        
        try:
            async with httpx.AsyncClient() as client:
                # 调用第三方重写API
                response = await client.post(
                    self.rewrite_api_url,
                    headers={
                        "Content-Type": "application/x-www-form-urlencoded"
                    },
                    data={
                        "text": text,
                        "auto": "0"
                    },
                    timeout=120.0
                )
                
                if response.status_code == 200:
                    result = response.json()
                    
                    # 从返回结果中提取paraphrase字段作为美化后的文本
                    beautified_text = result.get("paraphrase", text)
                    ai_score = result.get("ai_score", -1)
                    rewrite_count = result.get("rewrite_count", 0)
                    
                    execution_time = time.time() - start_time
                    
                    return {
                        "success": True,
                        "data": {
                            "original_text": text,
                            "beautified_text": beautified_text,
                            "style": style,  # 保持兼容性，虽然第三方API不支持风格选择
                            # "ai_score": ai_score,
                            # "rewrite_count": rewrite_count,
                            "execution_time": execution_time
                        }
                    }
                else:
                    return {
                        "success": False,
                        "error": f"API调用失败: {response.status_code} - {response.text}"
                    }
                    
        except httpx.TimeoutException:
            return {
                "success": False,
                "error": "请求超时，请稍后重试"
            }
        except httpx.RequestError as e:
            return {
                "success": False,
                "error": f"网络请求错误: {str(e)}"
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"文本美化失败: {str(e)}"
            }
    
    async def detect_language(self, text: str) -> Dict[str, Any]:
        """语言检测服务"""
        start_time = time.time()
        
        try:
            # 使用langdetect进行语言检测
            detected_lang = detect(text)
            lang_probabilities = detect_langs(text)
            
            # 语言代码到语言名称的映射
            lang_names = {
                'zh-cn': '简体中文',
                'zh-tw': '繁体中文',
                'en': '英语',
                'ja': '日语',
                'ko': '韩语',
                'fr': '法语',
                'de': '德语',
                'es': '西班牙语',
                'it': '意大利语',
                'pt': '葡萄牙语',
                'ru': '俄语',
                'ar': '阿拉伯语',
                'hi': '印地语',
                'th': '泰语',
                'vi': '越南语'
            }
            
            # 格式化概率结果
            probabilities = []
            for lang_prob in lang_probabilities:
                probabilities.append({
                    "language_code": lang_prob.lang,
                    "language_name": lang_names.get(lang_prob.lang, lang_prob.lang),
                    "probability": round(lang_prob.prob, 4)
                })
            
            execution_time = time.time() - start_time
            
            return {
                "success": True,
                "data": {
                    "text": text,
                    "detected_language": {
                        "code": detected_lang,
                        "name": lang_names.get(detected_lang, detected_lang)
                    },
                    "probabilities": probabilities,
                    "execution_time": execution_time
                }
            }
            
        except LangDetectException as e:
            return {
                "success": False,
                "error": f"语言检测失败: 文本太短或无法识别语言"
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"语言检测失败: {str(e)}"
            }
    
    async def text_to_image(self, text: str, style: str = "realistic", size: str = "1024x1024") -> Dict[str, Any]:
        """文本生成图片服务"""
        start_time = time.time()
        
        try:
            # 根据风格调整提示词
            style_modifiers = {
                "realistic": "photorealistic, high quality, detailed",
                "cartoon": "cartoon style, animated, colorful, fun",
                "artistic": "artistic, painterly, creative, expressive",
                "abstract": "abstract art, modern, conceptual, artistic"
            }
            
            style_modifier = style_modifiers.get(style, style_modifiers["realistic"])
            enhanced_prompt = f"{text}, {style_modifier}"
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.openrouter_base_url}/images/generations",
                    headers={
                        "Authorization": f"Bearer {self.openrouter_api_key}",
                        "Content-Type": "application/json"
                    },
                    json={
                        "model": "black-forest-labs/flux-1-schnell",
                        "prompt": enhanced_prompt,
                        "n": 1,
                        "size": size,
                        "quality": "standard"
                    },
                    timeout=60.0
                )
                
                if response.status_code == 200:
                    result = response.json()
                    image_url = result["data"][0]["url"]
                    
                    execution_time = time.time() - start_time
                    
                    return {
                        "success": True,
                        "data": {
                            "prompt": text,
                            "enhanced_prompt": enhanced_prompt,
                            "style": style,
                            "size": size,
                            "image_url": image_url,
                            "execution_time": execution_time
                        }
                    }
                else:
                    return {
                        "success": False,
                        "error": f"图片生成失败: {response.status_code}"
                    }
                    
        except Exception as e:
            return {
                "success": False,
                "error": f"图片生成失败: {str(e)}"
            }

    async def parse_json(self, text: str, format_type: str = "pretty", indent: int = 4) -> Dict[str, Any]:
        """JSON解析和格式化服务"""
        start_time = time.time()
        
        try:
            # 尝试解析JSON
            try:
                parsed_data = json.loads(text.strip())
                is_valid = True
                error_message = None
            except json.JSONDecodeError as e:
                is_valid = False
                error_message = f"JSON解析错误: {str(e)}"
                parsed_data = None
            
            # 根据格式化类型处理结果
            if is_valid and parsed_data is not None:
                if format_type == "pretty":
                    # 美化格式
                    formatted_json = json.dumps(
                        parsed_data, 
                        ensure_ascii=False, 
                        indent=indent,
                        separators=(',', ': ')
                    )
                elif format_type == "compact":
                    # 紧凑格式
                    formatted_json = json.dumps(
                        parsed_data, 
                        ensure_ascii=False, 
                        separators=(',', ':')
                    )
                elif format_type == "validate":
                    # 仅验证，返回原文
                    formatted_json = text.strip()
                else:
                    # 默认美化格式
                    formatted_json = json.dumps(
                        parsed_data, 
                        ensure_ascii=False, 
                        indent=indent,
                        separators=(',', ': ')
                    )
            else:
                formatted_json = text.strip()
            
            execution_time = time.time() - start_time
            
            return {
                "success": True,
                "data": {
                    "original_text": text,
                    "parsed_json": formatted_json,
                    "is_valid": is_valid,
                    "format_type": format_type,
                    "error_message": error_message,
                    "execution_time": execution_time
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"JSON解析失败: {str(e)}"
            }

    async def count_tokens(self, text: str, model: str = "gpt-4") -> Dict[str, Any]:
        """Token计数服务"""
        start_time = time.time()
        
        try:
            # 模型名称映射
            model_mappings = {
                "gpt-4": "gpt-4",
                "gpt-4-turbo": "gpt-4-1106-preview",
                "gpt-4-turbo-preview": "gpt-4-1106-preview",
                "gpt-4-vision": "gpt-4-vision-preview",
                "gpt-3.5-turbo": "gpt-3.5-turbo",
                "gpt-3.5-turbo-16k": "gpt-3.5-turbo-16k",
                "text-davinci-003": "text-davinci-003",
                "text-davinci-002": "text-davinci-002",
                "code-davinci-002": "code-davinci-002",
                "claude-3": "gpt-4",  # Claude使用类似gpt-4的计算方式
                "claude-3-opus": "gpt-4",
                "claude-3-sonnet": "gpt-4",
                "claude-3-haiku": "gpt-4"
            }
            
            # 获取实际的编码器模型
            encoder_model = model_mappings.get(model.lower(), "gpt-4")
            
            # 获取编码器
            try:
                encoding = tiktoken.encoding_for_model(encoder_model)
            except KeyError:
                # 如果模型不支持，使用cl100k_base编码（适用于gpt-4和gpt-3.5-turbo）
                encoding = tiktoken.get_encoding("cl100k_base")
            
            # 计算token数量
            tokens = encoding.encode(text)
            token_count = len(tokens)
            
            # 计算大概的字符数
            char_count = len(text)
            
            # 计算成本估算（基于不同模型的定价）
            cost_per_1k_tokens = {
                "gpt-4": 0.03,  # 输入token成本
                "gpt-4-turbo": 0.01,
                "gpt-3.5-turbo": 0.0015,
                "gpt-3.5-turbo-16k": 0.003,
                "claude-3": 0.015,
                "claude-3-opus": 0.015,
                "claude-3-sonnet": 0.003,
                "claude-3-haiku": 0.00025
            }
            
            base_cost = cost_per_1k_tokens.get(model.lower(), 0.01)
            estimated_cost = (token_count / 1000) * base_cost
            
            execution_time = time.time() - start_time
            
            return {
                "success": True,
                "data": {
                    "text": text[:100] + "..." if len(text) > 100 else text,  # 只显示前100个字符
                    "text_length": char_count,
                    "model": model,
                    "token_count": token_count,
                    "encoding_model": encoder_model,
                    "estimated_cost_usd": round(estimated_cost, 6),
                    "cost_per_1k_tokens": base_cost,
                    "token_to_char_ratio": round(token_count / char_count, 3) if char_count > 0 else 0,
                    "execution_time": execution_time
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"Token计数失败: {str(e)}"
            }


# 创建全局AI服务实例
ai_service = AIService() 