from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from fastapi import HTTPException, status
from ..models.rate_limit import RateLimit


class RateLimitService:
    @staticmethod
    def check_rate_limit(db: Session, ip_address: str, tool_slug: str) -> bool:
        """
        检查速率限制
        返回True表示可以调用，False表示被限制
        """
        now = datetime.utcnow()
        one_minute_ago = now - timedelta(minutes=1)
        
        # 查找该IP对该工具的最近调用记录
        rate_limit = db.query(RateLimit).filter(
            RateLimit.ip_address == ip_address,
            RateLimit.tool_slug == tool_slug
        ).first()
        
        if not rate_limit:
            # 首次调用，创建记录
            new_rate_limit = RateLimit(
                ip_address=ip_address,
                tool_slug=tool_slug,
                last_call_time=now,
                call_count=1
            )
            db.add(new_rate_limit)
            db.commit()
            return True
        
        # 检查是否在1分钟内
        if rate_limit.last_call_time > one_minute_ago:
            # 在1分钟内，检查调用次数
            if rate_limit.call_count >= 1:
                # 已达到限制
                remaining_time = 60 - (now - rate_limit.last_call_time).total_seconds()
                raise HTTPException(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    detail=f"速率限制：每分钟只能调用1次，请等待 {int(remaining_time)} 秒后重试"
                )
            else:
                # 更新调用次数
                rate_limit.call_count += 1
                rate_limit.last_call_time = now
                db.commit()
                return True
        else:
            # 超过1分钟，重置计数
            rate_limit.call_count = 1
            rate_limit.last_call_time = now
            db.commit()
            return True
    
    @staticmethod
    def reset_rate_limit(db: Session, ip_address: str, tool_slug: str):
        """重置速率限制（管理员功能）"""
        rate_limit = db.query(RateLimit).filter(
            RateLimit.ip_address == ip_address,
            RateLimit.tool_slug == tool_slug
        ).first()
        
        if rate_limit:
            db.delete(rate_limit)
            db.commit() 