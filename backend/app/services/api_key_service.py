import hashlib
from datetime import datetime
from typing import Optional, List, Dict, Any
from sqlalchemy.orm import Session
from fastapi import HTTPException

from ..models.api_key import APIKey
from ..models.user import User


class APIKeyService:
    @staticmethod
    def hash_api_key(api_key: str) -> str:
        """对API Key进行哈希处理"""
        return hashlib.sha256(api_key.encode()).hexdigest()

    @staticmethod
    def create_api_key(
        db: Session, 
        user_id: int, 
        name: str, 
        description: Optional[str] = None
    ) -> Dict[str, Any]:
        """创建新的API Key"""
        # 检查用户是否存在
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")

        # 检查用户的API Key数量限制（最多5个）
        existing_keys_count = db.query(APIKey).filter(
            APIKey.user_id == user_id,
            APIKey.is_active == True
        ).count()
        
        if existing_keys_count >= 5:
            raise HTTPException(status_code=400, detail="每个用户最多只能创建5个API Key")

        # 生成API Key
        full_key, prefix, key_body = APIKey.generate_api_key()
        key_hash = APIKeyService.hash_api_key(full_key)

        # 创建API Key记录
        api_key = APIKey(
            user_id=user_id,
            name=name,
            key_hash=key_hash,
            key_prefix=prefix,
            description=description,
            is_active=True
        )

        db.add(api_key)
        db.commit()
        db.refresh(api_key)

        return {
            "id": api_key.id,
            "name": api_key.name,
            "key": full_key,  # 只在创建时返回完整的key
            "prefix": api_key.key_prefix,
            "description": api_key.description,
            "created_at": api_key.created_at
        }

    @staticmethod
    def get_user_api_keys(db: Session, user_id: int) -> List[Dict[str, Any]]:
        """获取用户的所有API Key"""
        api_keys = db.query(APIKey).filter(
            APIKey.user_id == user_id,
            APIKey.is_active == True
        ).order_by(APIKey.created_at.desc()).all()

        return [
            {
                "id": key.id,
                "name": key.name,
                "prefix": key.key_prefix,
                "description": key.description,
                "usage_count": key.usage_count,
                "last_used_at": key.last_used_at,
                "created_at": key.created_at
            }
            for key in api_keys
        ]

    @staticmethod
    def delete_api_key(db: Session, user_id: int, key_id: int) -> bool:
        """删除API Key"""
        api_key = db.query(APIKey).filter(
            APIKey.id == key_id,
            APIKey.user_id == user_id,
            APIKey.is_active == True
        ).first()

        if not api_key:
            raise HTTPException(status_code=404, detail="API Key不存在")

        api_key.is_active = False
        db.commit()
        return True

    @staticmethod
    def authenticate_api_key(db: Session, api_key: str) -> Optional[User]:
        """通过API Key认证用户"""
        if not api_key or not api_key.startswith("sk-"):
            return None

        key_hash = APIKeyService.hash_api_key(api_key)
        
        api_key_record = db.query(APIKey).filter(
            APIKey.key_hash == key_hash,
            APIKey.is_active == True
        ).first()

        if not api_key_record:
            return None

        # 更新使用统计
        api_key_record.usage_count += 1
        api_key_record.last_used_at = datetime.utcnow()
        db.commit()

        # 返回关联的用户
        return api_key_record.user

    @staticmethod
    def update_api_key(
        db: Session, 
        user_id: int, 
        key_id: int, 
        name: Optional[str] = None,
        description: Optional[str] = None
    ) -> Dict[str, Any]:
        """更新API Key信息"""
        api_key = db.query(APIKey).filter(
            APIKey.id == key_id,
            APIKey.user_id == user_id,
            APIKey.is_active == True
        ).first()

        if not api_key:
            raise HTTPException(status_code=404, detail="API Key不存在")

        if name is not None:
            api_key.name = name
        if description is not None:
            api_key.description = description

        db.commit()
        db.refresh(api_key)

        return {
            "id": api_key.id,
            "name": api_key.name,
            "prefix": api_key.key_prefix,
            "description": api_key.description,
            "usage_count": api_key.usage_count,
            "last_used_at": api_key.last_used_at,
            "created_at": api_key.created_at
        } 