from fastapi import APIRouter, HTTPException, Depends, Request
from sqlalchemy.orm import Session
from typing import List, Optional

from ....core.database import get_db
from ....core.auth import get_current_user_or_none, check_credits_and_deduct
from ....models.api_tool import APITool, APIToolCategory
from ....models.user import User
from ....schemas.api_tool import (
    APIToolList, 
    APICallResponse, 
    TextBeautifyRequest, 
    LanguageDetectRequest, 
    TextToImageRequest, 
    JsonParseRequest,
    TokenCountRequest
)
from ....services.ai_service import ai_service
from ....services.rate_limit_service import RateLimitService


router = APIRouter()


def get_client_ip(request: Request) -> str:
    """获取客户端IP地址"""
    x_forwarded_for = request.headers.get("x-forwarded-for")
    if x_forwarded_for:
        return x_forwarded_for.split(",")[0].strip()
    return request.client.host if request.client else "unknown"


@router.get("/", response_model=APIToolList)
async def get_api_tools(
    page: int = 1,
    limit: int = 20,
    category: str = None,
    db: Session = Depends(get_db)
):
    """获取API工具列表"""
    try:
        # 计算偏移量
        offset = (page - 1) * limit
        
        # 构建查询
        query = db.query(APITool).filter(APITool.is_active == True)
        
        if category:
            # URL解码分类名称
            import urllib.parse
            decoded_category = urllib.parse.unquote(category)
            query = query.filter(APITool.category == decoded_category)
        
        # 获取总数
        total = query.count()
        
        # 获取分页数据
        tools = query.offset(offset).limit(limit).all()
        
        # 转换为响应格式
        tool_list = []
        for tool in tools:
            tool_data = {
                "id": tool.id,
                "name": tool.name,
                "slug": tool.slug,
                "description": tool.description,
                "category": tool.category,
                "icon": tool.icon,
                "endpoint": tool.endpoint,
                "method": tool.method,
                "cost_per_request": tool.cost_per_request,
                "rate_limit": "每分钟1次（未登录用户）",
                "total_calls": tool.total_calls,
                "success_rate": tool.success_rate,
                "created_at": tool.created_at
            }
            tool_list.append(tool_data)
        
        return APIToolList(
            success=True,
            tools=tool_list,
            total=total,
            page=page,
            limit=limit,
            has_next=offset + limit < total
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取工具列表失败: {str(e)}")


@router.get("/categories")
async def get_tool_categories(db: Session = Depends(get_db)):
    """获取工具分类列表"""
    try:
        # 从数据库获取分类
        categories = db.query(APIToolCategory).filter(APIToolCategory.is_active == True).all()
        
        # 统计每个分类的工具数量
        category_data = []
        for category in categories:
            tool_count = db.query(APITool).filter(
                APITool.category == category.name,
                APITool.is_active == True
            ).count()
            
            category_data.append({
                "name": category.name,
                "count": tool_count,
                "icon": category.icon,
                "description": category.description
            })
        
        return {
            "success": True,
            "data": category_data
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取分类列表失败: {str(e)}")


@router.post("/text-beautify", response_model=APICallResponse)
async def beautify_text(
    request: TextBeautifyRequest, 
    http_request: Request,
    db: Session = Depends(get_db)
):
    """文本美化API"""
    try:
        # 尝试获取认证用户
        current_user = get_current_user_or_none(http_request, db)
        
        if current_user:
            # 认证用户：检查积分并扣除
            credits_required = 2.0
            check_credits_and_deduct(db, current_user, credits_required)
        else:
            # 未认证用户：检查速率限制
            client_ip = get_client_ip(http_request)
            RateLimitService.check_rate_limit(db, client_ip, "text-beautify")
            credits_required = 0.0
        
        result = await ai_service.beautify_text(request.text, request.style)
        
        if result["success"]:
            return APICallResponse(
                success=True,
                data=result["data"],
                credits_used=credits_required,
                execution_time=result["data"]["execution_time"]
            )
        else:
            raise HTTPException(status_code=400, detail=result["error"])
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/language-detect", response_model=APICallResponse)
async def detect_language(
    request: LanguageDetectRequest,
    http_request: Request,
    db: Session = Depends(get_db)
):
    """语言检测API"""
    try:
        # 尝试获取认证用户
        current_user = get_current_user_or_none(http_request, db)
        
        if current_user:
            # 认证用户：检查积分并扣除
            credits_required = 1.0
            check_credits_and_deduct(db, current_user, credits_required)
        else:
            # 未认证用户：检查速率限制
            client_ip = get_client_ip(http_request)
            RateLimitService.check_rate_limit(db, client_ip, "language-detect")
            credits_required = 0.0
        
        result = await ai_service.detect_language(request.text)
        
        if result["success"]:
            return APICallResponse(
                success=True,
                data=result["data"],
                credits_used=credits_required,
                execution_time=result["data"]["execution_time"]
            )
        else:
            raise HTTPException(status_code=400, detail=result["error"])
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/text-to-image", response_model=APICallResponse)
async def text_to_image(
    request: TextToImageRequest,
    http_request: Request,
    db: Session = Depends(get_db)
):
    """文本生成图片API"""
    try:
        # 尝试获取认证用户
        current_user = get_current_user_or_none(http_request, db)
        
        if current_user:
            # 认证用户：检查积分并扣除
            credits_required = 5.0
            check_credits_and_deduct(db, current_user, credits_required)
        else:
            # 未认证用户：检查速率限制
            client_ip = get_client_ip(http_request)
            RateLimitService.check_rate_limit(db, client_ip, "text-to-image")
            credits_required = 0.0
        
        result = await ai_service.text_to_image(request.text, request.style, request.size)
        
        if result["success"]:
            return APICallResponse(
                success=True,
                data=result["data"],
                credits_used=credits_required,
                execution_time=result["data"]["execution_time"]
            )
        else:
            raise HTTPException(status_code=400, detail=result["error"])
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/json-parse", response_model=APICallResponse)
async def parse_json(
    request: JsonParseRequest,
    http_request: Request,
    db: Session = Depends(get_db)
):
    """JSON解析API"""
    try:
        # 尝试获取认证用户
        current_user = get_current_user_or_none(http_request, db)
        
        if current_user:
            # 认证用户：检查积分并扣除
            credits_required = 1.0
            check_credits_and_deduct(db, current_user, credits_required)
        else:
            # 未认证用户：检查速率限制
            client_ip = get_client_ip(http_request)
            RateLimitService.check_rate_limit(db, client_ip, "json-parse")
            credits_required = 0.0
        
        result = await ai_service.parse_json(request.text, request.format_type, request.indent)
        
        if result["success"]:
            return APICallResponse(
                success=True,
                data=result["data"],
                credits_used=credits_required,
                execution_time=result["data"]["execution_time"]
            )
        else:
            raise HTTPException(status_code=400, detail=result["error"])
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/token-count", response_model=APICallResponse)
async def count_tokens(
    request: TokenCountRequest,
    http_request: Request,
    db: Session = Depends(get_db)
):
    """Token计数API"""
    try:
        # 尝试获取认证用户
        current_user = get_current_user_or_none(http_request, db)
        
        if current_user:
            # 认证用户：检查积分并扣除
            credits_required = 0.5  # token计数消耗较少积分
            check_credits_and_deduct(db, current_user, credits_required)
        else:
            # 未认证用户：检查速率限制
            client_ip = get_client_ip(http_request)
            RateLimitService.check_rate_limit(db, client_ip, "token-count")
            credits_required = 0.0
        
        result = await ai_service.count_tokens(request.text, request.model)
        
        if result["success"]:
            return APICallResponse(
                success=True,
                data=result["data"],
                credits_used=credits_required,
                execution_time=result["data"]["execution_time"]
            )
        else:
            raise HTTPException(status_code=400, detail=result["error"])
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{tool_slug}")
async def get_tool_detail(tool_slug: str, db: Session = Depends(get_db)):
    """获取工具详情"""
    try:
        # 从数据库获取工具信息
        tool = db.query(APITool).filter(
            APITool.slug == tool_slug,
            APITool.is_active == True
        ).first()
        
        if not tool:
            raise HTTPException(status_code=404, detail="工具不存在")
        
        # 构建工具详情
        tool_detail = {
            "id": tool.id,
            "name": tool.name,
            "slug": tool.slug,
            "description": tool.description,
            "category": tool.category,
            "icon": tool.icon,
            "endpoint": tool.endpoint,
            "method": tool.method,
            "cost_per_request": tool.cost_per_request,
            "rate_limit": "每分钟1次（未登录用户）或积分消耗（API Key用户）",
            "input_schema": tool.input_schema,
            "output_schema": tool.output_schema,
            "total_calls": tool.total_calls,
            "success_rate": tool.success_rate,
            "created_at": tool.created_at,
            "updated_at": tool.updated_at
        }
        
        # 添加示例数据（从example_request获取）
        if tool.example_request:
            tool_detail["example_request"] = tool.example_request
        elif tool.input_schema and "properties" in tool.input_schema:
            # 如果没有example_request，从input_schema生成
            example_request = {}
            for field, config in tool.input_schema["properties"].items():
                if "example" in config:
                    example_request[field] = config["example"]
                elif "default" in config:
                    example_request[field] = config["default"]
            
            if example_request:
                tool_detail["example_request"] = example_request
        
        return {"success": True, "data": tool_detail}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取工具详情失败: {str(e)}")