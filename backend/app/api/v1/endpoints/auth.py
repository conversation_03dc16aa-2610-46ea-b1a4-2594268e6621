from datetime import timedel<PERSON>
from typing import Optional
from fastapi import APIRouter, HTTPException, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from pydantic import BaseModel, EmailStr
import secrets
import hashlib
from datetime import datetime, timedelta
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIME<PERSON>ultipart
import jwt
from google.auth.transport import requests as google_requests
from google.oauth2 import id_token

from ....core.database import get_db
from ....core.config import settings
from ....models.user import User
from ....schemas.user import User<PERSON><PERSON>, User<PERSON>og<PERSON>, Token, UserProfile
from ....core.security import verify_password, get_password_hash, create_access_token

router = APIRouter()
security = HTTPBearer()

# 验证码存储（生产环境应使用Redis）
verification_codes = {}

class SendCodeRequest(BaseModel):
    email: EmailStr
    type: str  # 'login' or 'register'

class LoginWithCodeRequest(BaseModel):
    email: EmailStr
    verification_code: str

class RegisterWithCodeRequest(BaseModel):
    email: EmailStr
    username: str
    full_name: Optional[str] = None
    verification_code: str

class GoogleLoginRequest(BaseModel):
    credential: str

def generate_verification_code() -> str:
    """生成6位验证码"""
    return str(secrets.randbelow(900000) + 100000)

def send_email_verification_code(email: str, code: str, type: str = "login"):
    """发送邮箱验证码（这里使用模拟发送）"""
    # 在实际生产环境中，这里应该集成真实的邮件服务
    # 比如 SendGrid, AWS SES, 或其他邮件服务提供商
    
    print(f"发送验证码到 {email}: {code} (类型: {type})")
    
    # 这里只是模拟发送成功
    # 在实际环境中，应该有真实的邮件发送逻辑
    return True

def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security), db: Session = Depends(get_db)):
    """获取当前用户"""
    try:
        token = credentials.credentials
        payload = jwt.decode(token, settings.JWT_SECRET_KEY, algorithms=[settings.JWT_ALGORITHM])
        user_id: int = payload.get("sub")
        if user_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的认证令牌",
                headers={"WWW-Authenticate": "Bearer"},
            )
    except (jwt.exceptions.JWTException, jwt.exceptions.JWTDecodeError):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的认证令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    user = db.query(User).filter(User.id == user_id).first()
    if user is None:
        raise HTTPException(status_code=404, detail="用户不存在")
    return user

@router.post("/send-code")
async def send_verification_code(request: SendCodeRequest, db: Session = Depends(get_db)):
    """发送验证码"""
    try:
        # 检查邮箱是否已存在（仅注册时检查）
        if request.type == "register":
            existing_user = db.query(User).filter(User.email == request.email).first()
            if existing_user:
                raise HTTPException(status_code=400, detail="该邮箱已被注册")
        elif request.type == "login":
            # 登录时检查用户是否存在
            user = db.query(User).filter(User.email == request.email).first()
            if not user:
                raise HTTPException(status_code=404, detail="用户不存在")
        
        # 生成验证码
        code = generate_verification_code()
        
        # 存储验证码（设置过期时间为5分钟）
        verification_codes[request.email] = {
            "code": code,
            "type": request.type,
            "expires_at": datetime.utcnow() + timedelta(minutes=5)
        }
        
        # 发送验证码
        if send_email_verification_code(request.email, code, request.type):
            return {
                "success": True,
                "message": "验证码已发送到您的邮箱",
                "expires_in": 300  # 5分钟
            }
        else:
            raise HTTPException(status_code=500, detail="验证码发送失败")
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"发送验证码失败: {str(e)}")

@router.post("/login", response_model=Token)
async def login_with_code(request: LoginWithCodeRequest, db: Session = Depends(get_db)):
    """使用验证码登录"""
    try:
        # 验证验证码
        stored_code_info = verification_codes.get(request.email)
        if not stored_code_info:
            raise HTTPException(status_code=400, detail="验证码不存在或已过期")
        
        if stored_code_info["expires_at"] < datetime.utcnow():
            del verification_codes[request.email]
            raise HTTPException(status_code=400, detail="验证码已过期")
        
        if stored_code_info["code"] != request.verification_code:
            raise HTTPException(status_code=400, detail="验证码错误")
        
        if stored_code_info["type"] != "login":
            raise HTTPException(status_code=400, detail="验证码类型错误")
        
        # 查找用户
        user = db.query(User).filter(User.email == request.email).first()
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")
        
        if not user.is_active:
            raise HTTPException(status_code=400, detail="用户账户已被禁用")
        
        # 更新最后登录时间
        user.last_login = datetime.utcnow()
        db.commit()
        
        # 创建访问令牌
        access_token = create_access_token(data={"sub": str(user.id)})
        
        # 删除已使用的验证码
        del verification_codes[request.email]
        
        return Token(
            access_token=access_token,
            token_type="bearer",
            expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            user=UserProfile(
                id=user.id,
                email=user.email,
                username=user.username,
                full_name=user.full_name,
                avatar_url=user.avatar_url,
                credits=user.credits,
                created_at=user.created_at
            )
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"登录失败: {str(e)}")

@router.post("/register", response_model=Token)
async def register_with_code(request: RegisterWithCodeRequest, db: Session = Depends(get_db)):
    """使用验证码注册"""
    try:
        # 验证验证码
        stored_code_info = verification_codes.get(request.email)
        if not stored_code_info:
            raise HTTPException(status_code=400, detail="验证码不存在或已过期")
        
        if stored_code_info["expires_at"] < datetime.utcnow():
            del verification_codes[request.email]
            raise HTTPException(status_code=400, detail="验证码已过期")
        
        if stored_code_info["code"] != request.verification_code:
            raise HTTPException(status_code=400, detail="验证码错误")
        
        if stored_code_info["type"] != "register":
            raise HTTPException(status_code=400, detail="验证码类型错误")
        
        # 检查邮箱是否已存在
        if db.query(User).filter(User.email == request.email).first():
            raise HTTPException(status_code=400, detail="该邮箱已被注册")
        
        # 检查用户名是否已存在
        if db.query(User).filter(User.username == request.username).first():
            raise HTTPException(status_code=400, detail="该用户名已被使用")
        
        # 创建新用户
        # 由于使用验证码注册，我们生成一个随机密码
        random_password = secrets.token_urlsafe(32)
        
        new_user = User(
            email=request.email,
            username=request.username,
            full_name=request.full_name,
            hashed_password=get_password_hash(random_password),
            is_active=True,
            is_verified=True,  # 通过验证码验证的用户直接设为已验证
            credits=100.0  # 新用户赠送100积分
        )
        
        db.add(new_user)
        db.commit()
        db.refresh(new_user)
        
        # 创建访问令牌
        access_token = create_access_token(data={"sub": str(new_user.id)})
        
        # 删除已使用的验证码
        del verification_codes[request.email]
        
        return Token(
            access_token=access_token,
            token_type="bearer",
            expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            user=UserProfile(
                id=new_user.id,
                email=new_user.email,
                username=new_user.username,
                full_name=new_user.full_name,
                avatar_url=new_user.avatar_url,
                credits=new_user.credits,
                created_at=new_user.created_at
            )
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"注册失败: {str(e)}")

@router.post("/google", response_model=Token)
async def google_login(request: GoogleLoginRequest, db: Session = Depends(get_db)):
    """谷歌OAuth登录"""
    try:
        # 验证谷歌ID令牌
        try:
            # 这里需要配置谷歌客户端ID
            CLIENT_ID = getattr(settings, 'GOOGLE_CLIENT_ID', None)
            if not CLIENT_ID:
                raise HTTPException(status_code=500, detail="谷歌登录配置错误")
            
            idinfo = id_token.verify_oauth2_token(
                request.credential, google_requests.Request(), CLIENT_ID)
            
            if idinfo['iss'] not in ['accounts.google.com', 'https://accounts.google.com']:
                raise ValueError('错误的发行者')
                
        except ValueError as e:
            raise HTTPException(status_code=400, detail=f"无效的谷歌令牌: {str(e)}")
        
        # 提取用户信息
        email = idinfo.get('email')
        name = idinfo.get('name')
        picture = idinfo.get('picture')
        
        if not email:
            raise HTTPException(status_code=400, detail="无法获取邮箱信息")
        
        # 查找或创建用户
        user = db.query(User).filter(User.email == email).first()
        
        if not user:
            # 创建新用户
            username = email.split('@')[0]  # 使用邮箱前缀作为用户名
            
            # 确保用户名唯一
            counter = 1
            original_username = username
            while db.query(User).filter(User.username == username).first():
                username = f"{original_username}{counter}"
                counter += 1
            
            # 生成随机密码
            random_password = secrets.token_urlsafe(32)
            
            user = User(
                email=email,
                username=username,
                full_name=name,
                avatar_url=picture,
                hashed_password=get_password_hash(random_password),
                is_active=True,
                is_verified=True,  # 谷歌账户认为已验证
                credits=100.0  # 新用户赠送100积分
            )
            
            db.add(user)
            db.commit()
            db.refresh(user)
        else:
            # 更新最后登录时间和头像
            user.last_login = datetime.utcnow()
            if picture and not user.avatar_url:
                user.avatar_url = picture
            db.commit()
        
        if not user.is_active:
            raise HTTPException(status_code=400, detail="用户账户已被禁用")
        
        # 创建访问令牌
        access_token = create_access_token(data={"sub": str(user.id)})
        
        return Token(
            access_token=access_token,
            token_type="bearer",
            expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            user=UserProfile(
                id=user.id,
                email=user.email,
                username=user.username,
                full_name=user.full_name,
                avatar_url=user.avatar_url,
                credits=user.credits,
                created_at=user.created_at
            )
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"谷歌登录失败: {str(e)}")

@router.get("/me", response_model=UserProfile)
async def get_current_user_info(current_user: User = Depends(get_current_user)):
    """获取当前用户信息"""
    return UserProfile(
        id=current_user.id,
        email=current_user.email,
        username=current_user.username,
        full_name=current_user.full_name,
        avatar_url=current_user.avatar_url,
        credits=current_user.credits,
        created_at=current_user.created_at
    )

@router.post("/refresh", response_model=Token)
async def refresh_token(current_user: User = Depends(get_current_user)):
    """刷新访问令牌"""
    access_token = create_access_token(data={"sub": str(current_user.id)})
    
    return Token(
        access_token=access_token,
        token_type="bearer",
        expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        user=UserProfile(
            id=current_user.id,
            email=current_user.email,
            username=current_user.username,
            full_name=current_user.full_name,
            avatar_url=current_user.avatar_url,
            credits=current_user.credits,
            created_at=current_user.created_at
        )
    )

@router.post("/logout")
async def logout():
    """登出（客户端处理）"""
    return {"message": "已成功登出"} 