from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List

from ....core.database import get_db
from ....core.auth import get_current_user
from ....models.user import User
from ....schemas.api_key import (
    APIKeyCreate, 
    APIKeyUpdate, 
    APIKeyResponse, 
    APIKeyCreateResponse,
    APIKeyListResponse
)
from ....services.api_key_service import APIKeyService


router = APIRouter()


@router.post("/", response_model=APIKeyCreateResponse)
async def create_api_key(
    request: APIKeyCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """创建新的API Key"""
    try:
        result = APIKeyService.create_api_key(
            db=db,
            user_id=current_user.id,
            name=request.name,
            description=request.description
        )
        return APIKeyCreateResponse(**result)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建API Key失败: {str(e)}")


@router.get("/", response_model=APIKeyListResponse)
async def get_api_keys(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取用户的所有API Key"""
    try:
        api_keys = APIKeyService.get_user_api_keys(db=db, user_id=current_user.id)
        return APIKeyListResponse(success=True, data=api_keys)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取API Key列表失败: {str(e)}")


@router.put("/{key_id}", response_model=APIKeyResponse)
async def update_api_key(
    key_id: int,
    request: APIKeyUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新API Key信息"""
    try:
        result = APIKeyService.update_api_key(
            db=db,
            user_id=current_user.id,
            key_id=key_id,
            name=request.name,
            description=request.description
        )
        return APIKeyResponse(**result)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新API Key失败: {str(e)}")


@router.delete("/{key_id}")
async def delete_api_key(
    key_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """删除API Key"""
    try:
        APIKeyService.delete_api_key(db=db, user_id=current_user.id, key_id=key_id)
        return {"success": True, "message": "API Key已删除"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除API Key失败: {str(e)}") 