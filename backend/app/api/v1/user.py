from fastapi import APIRouter, HTTPException, Depends
from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel

router = APIRouter()

# 用户数据模型
class UserSubscription(BaseModel):
    plan: str
    expires_at: datetime
    status: str

class UserUsageStats(BaseModel):
    total_requests: int
    this_month_requests: int
    favorite_tool: str

class UserProfile(BaseModel):
    id: int
    username: str
    email: str
    credits: int
    subscription: UserSubscription
    usage_stats: UserUsageStats
    created_at: datetime

# 模拟用户数据
MOCK_USER_DATA = {
    "id": 1,
    "username": "demo_user",
    "email": "<EMAIL>",
    "credits": 150,
    "subscription": {
        "plan": "premium",
        "expires_at": "2024-12-31T23:59:59Z",
        "status": "active"
    },
    "usage_stats": {
        "total_requests": 45,
        "this_month_requests": 12,
        "favorite_tool": "text-beautify"
    },
    "created_at": "2024-01-01T00:00:00Z"
}

@router.get("/profile", response_model=UserProfile)
async def get_user_profile():
    """获取用户资料"""
    try:
        # 在实际应用中，这里应该从数据库获取当前登录用户的信息
        # 现在返回模拟数据用于演示
        return MOCK_USER_DATA
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取用户信息失败: {str(e)}")

@router.get("/credits")
async def get_user_credits():
    """获取用户积分信息"""
    try:
        return {
            "credits": MOCK_USER_DATA["credits"],
            "subscription_plan": MOCK_USER_DATA["subscription"]["plan"],
            "subscription_status": MOCK_USER_DATA["subscription"]["status"]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取积分信息失败: {str(e)}")

@router.get("/usage-stats")
async def get_usage_stats():
    """获取使用统计"""
    try:
        return MOCK_USER_DATA["usage_stats"]
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取使用统计失败: {str(e)}")

@router.post("/credits/recharge")
async def recharge_credits(amount: int):
    """充值积分"""
    try:
        if amount <= 0:
            raise HTTPException(status_code=400, detail="充值金额必须大于0")
        
        # 在实际应用中，这里应该处理支付逻辑
        # 现在只是模拟充值成功
        new_credits = MOCK_USER_DATA["credits"] + amount
        MOCK_USER_DATA["credits"] = new_credits
        
        return {
            "success": True,
            "message": f"成功充值 {amount} 积分",
            "new_balance": new_credits
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"充值失败: {str(e)}")

@router.get("/activities")
async def get_user_activities():
    """获取用户最近活动"""
    try:
        # 模拟活动数据
        activities = [
            {
                "id": 1,
                "type": "tool_usage",
                "description": "使用文本美化工具",
                "details": "消耗 2 积分",
                "timestamp": "2024-01-15T14:30:00Z"
            },
            {
                "id": 2,
                "type": "tool_usage",
                "description": "使用语言检测工具",
                "details": "消耗 1 积分",
                "timestamp": "2024-01-14T10:15:00Z"
            },
            {
                "id": 3,
                "type": "recharge",
                "description": "充值积分",
                "details": "获得 100 积分",
                "timestamp": "2024-01-12T16:45:00Z"
            },
            {
                "id": 4,
                "type": "tool_usage",
                "description": "使用文本生成图片工具",
                "details": "消耗 5 积分",
                "timestamp": "2024-01-11T09:20:00Z"
            }
        ]
        
        return {
            "activities": activities,
            "total": len(activities)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取活动记录失败: {str(e)}") 