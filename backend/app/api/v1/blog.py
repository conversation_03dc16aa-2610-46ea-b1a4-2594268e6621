from fastapi import APIRouter, HTTPException, Query
from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel

router = APIRouter()

# 博客文章数据模型
class BlogPost(BaseModel):
    id: int
    title: str
    slug: str
    excerpt: str
    content: str
    author: str
    published_at: datetime
    tags: List[str]
    read_time: int

class BlogPostsResponse(BaseModel):
    posts: List[BlogPost]
    total: int
    page: int
    limit: int

# 模拟博客数据
MOCK_BLOG_POSTS = [
    {
        "id": 1,
        "title": "AI工具使用指南",
        "slug": "ai-tools-guide",
        "excerpt": "了解如何有效使用我们的AI工具来提高工作效率",
        "content": """# AI工具使用指南

欢迎使用我们的AI工具平台！本指南将帮助您快速上手并充分利用我们提供的各种AI工具。

## 主要功能

我们的平台提供以下核心功能：

### 1. 文本美化
- 使用先进的AI技术改进文本表达
- 支持多种风格：专业、休闲、正式、创意
- 适用于邮件、文档、社交媒体等场景

### 2. 语言检测
- 快速识别文本语言
- 支持100+种语言
- 高准确率检测

### 3. 文本生成图片
- 将文本描述转换为精美图片
- 多种艺术风格可选
- 高质量输出

## 使用技巧

1. **明确需求**：在使用工具前，明确您的具体需求
2. **合理设置参数**：根据场景选择合适的参数
3. **多次尝试**：不同的输入可能产生不同的效果

## 积分系统

- 每个工具调用都会消耗相应积分
- 新用户注册即可获得免费积分
- 支持多种充值方式

开始您的AI之旅吧！""",
        "author": "AI Tools Team",
        "published_at": "2024-01-15T10:00:00Z",
        "tags": ["AI", "教程", "工具"],
        "read_time": 5
    },
    {
        "id": 2,
        "title": "文本美化功能详解",
        "slug": "text-beautify-guide",
        "excerpt": "深入了解文本美化功能的使用方法和最佳实践",
        "content": """# 文本美化功能详解

文本美化是我们最受欢迎的功能之一，它能够帮助您将普通的文本转换为更加优雅、专业的表达。

## 功能特点

### 智能改写
- 保持原意不变的前提下优化表达
- 提升文本的可读性和专业度
- 自动纠正语法错误

### 多种风格
- **专业风格**：适用于商务邮件、报告
- **休闲风格**：适用于日常交流
- **正式风格**：适用于官方文档
- **创意风格**：适用于营销文案

## 使用示例

**原文**：这个产品很好用
**美化后**：这款产品具有卓越的实用性和优异的用户体验

## 最佳实践

1. 根据使用场景选择合适的风格
2. 对于重要文档，建议多次尝试不同风格
3. 结合人工审核确保最佳效果

立即体验文本美化功能，让您的文字更加出色！""",
        "author": "AI Tools Team",
        "published_at": "2024-01-10T14:30:00Z",
        "tags": ["文本处理", "美化", "AI"],
        "read_time": 8
    },
    {
        "id": 3,
        "title": "语言检测技术原理",
        "slug": "language-detection-tech",
        "excerpt": "探索语言检测背后的技术原理和应用场景",
        "content": """# 语言检测技术原理

语言检测是自然语言处理领域的一个重要分支，它能够自动识别文本所使用的语言。

## 技术原理

### 统计方法
- 基于字符频率分析
- N-gram模型
- 贝叶斯分类器

### 机器学习方法
- 特征提取
- 支持向量机
- 神经网络

## 应用场景

1. **多语言网站**：自动切换语言界面
2. **内容管理**：按语言分类文档
3. **翻译服务**：确定源语言
4. **数据分析**：多语言数据处理

## 准确率优化

我们的语言检测工具通过以下方式提升准确率：

- 大规模训练数据
- 多模型融合
- 实时优化算法

体验我们的语言检测功能，享受高精度的语言识别服务！""",
        "author": "技术团队",
        "published_at": "2024-01-05T09:15:00Z",
        "tags": ["技术", "语言检测", "机器学习"],
        "read_time": 6
    }
]

@router.get("/posts", response_model=BlogPostsResponse)
async def get_blog_posts(
    page: int = Query(1, ge=1, description="页码"),
    limit: int = Query(10, ge=1, le=50, description="每页数量")
):
    """获取博客文章列表"""
    try:
        # 计算分页
        start_idx = (page - 1) * limit
        end_idx = start_idx + limit
        
        # 获取分页数据
        paginated_posts = MOCK_BLOG_POSTS[start_idx:end_idx]
        
        return {
            "posts": paginated_posts,
            "total": len(MOCK_BLOG_POSTS),
            "page": page,
            "limit": limit
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取博客文章失败: {str(e)}")

@router.get("/posts/{slug}", response_model=BlogPost)
async def get_blog_post(slug: str):
    """根据slug获取博客文章详情"""
    try:
        # 查找文章
        post = next((post for post in MOCK_BLOG_POSTS if post["slug"] == slug), None)
        
        if not post:
            raise HTTPException(status_code=404, detail="文章不存在")
        
        return post
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取文章详情失败: {str(e)}") 