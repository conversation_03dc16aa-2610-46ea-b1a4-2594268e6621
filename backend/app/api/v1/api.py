from fastapi import APIRouter

from .endpoints import ai_tools, auth, api_keys
from . import blog, user

api_router = APIRouter()

# 认证相关路由
api_router.include_router(
    auth.router,
    prefix="/auth",
    tags=["认证"]
)

api_router.include_router(
    ai_tools.router, 
    prefix="/ai-tools", 
    tags=["AI工具"]
)

api_router.include_router(
    api_keys.router,
    prefix="/api-keys",
    tags=["API密钥"]
)

api_router.include_router(
    blog.router,
    prefix="/blog",
    tags=["博客"]
)

api_router.include_router(
    user.router,
    prefix="/user",
    tags=["用户"]
) 