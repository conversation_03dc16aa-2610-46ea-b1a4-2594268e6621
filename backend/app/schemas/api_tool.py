from typing import Optional, Dict, Any, List
from datetime import datetime
from pydantic import BaseModel


class APIToolBase(BaseModel):
    name: str
    description: Optional[str] = None
    category: str
    icon: Optional[str] = None
    cost_per_request: float = 1.0


class APIToolCreate(APIToolBase):
    endpoint: str
    method: str = "POST"
    input_schema: Optional[Dict[str, Any]] = None
    output_schema: Optional[Dict[str, Any]] = None


class APIToolUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    category: Optional[str] = None
    icon: Optional[str] = None
    cost_per_request: Optional[float] = None
    is_active: Optional[bool] = None


class APITool(APIToolBase):
    id: int
    slug: str
    endpoint: str
    method: str
    rate_limit: Optional[str] = None
    total_calls: int
    success_rate: float
    created_at: datetime
    
    class Config:
        from_attributes = True


class APIToolList(BaseModel):
    success: bool
    tools: List[APITool]
    total: int
    page: int
    limit: int
    has_next: bool


# API调用相关schemas
class TextBeautifyRequest(BaseModel):
    text: str
    style: Optional[str] = "professional"  # professional, casual, formal, creative


class LanguageDetectRequest(BaseModel):
    text: str


class TextToImageRequest(BaseModel):
    text: str
    style: Optional[str] = "realistic"  # realistic, cartoon, artistic, abstract
    size: Optional[str] = "1024x1024"  # 1024x1024, 512x512, 256x256


class JsonParseRequest(BaseModel):
    text: str
    format_type: Optional[str] = "pretty"  # pretty, compact, validate
    indent: Optional[int] = 4  # 缩进空格数


class TokenCountRequest(BaseModel):
    text: str
    model: Optional[str] = "gpt-4"  # gpt-4, gpt-3.5-turbo, gpt-4-turbo, claude-3, etc.


class APICallRequest(BaseModel):
    tool_id: int
    parameters: Dict[str, Any]


class APICallResponse(BaseModel):
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    credits_used: float
    execution_time: float


class APIUsageBase(BaseModel):
    user_id: int
    tool_id: int
    request_data: Dict[str, Any]
    credits_used: float


class APIUsage(APIUsageBase):
    id: int
    response_data: Optional[Dict[str, Any]]
    status: str
    error_message: Optional[str]
    execution_time: Optional[float]
    created_at: datetime
    completed_at: Optional[datetime]
    
    class Config:
        from_attributes = True 