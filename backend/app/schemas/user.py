from typing import Optional
from datetime import datetime
from pydantic import BaseModel, EmailStr


class UserBase(BaseModel):
    email: EmailStr
    username: str
    full_name: Optional[str] = None


class UserCreate(UserBase):
    password: str


class UserUpdate(BaseModel):
    email: Optional[EmailStr] = None
    username: Optional[str] = None
    full_name: Optional[str] = None
    avatar_url: Optional[str] = None


class UserLogin(BaseModel):
    email: EmailStr
    password: str


class User(UserBase):
    id: int
    avatar_url: Optional[str]
    is_active: bool
    is_verified: bool
    credits: float
    total_credits_purchased: float
    total_credits_used: float
    created_at: datetime
    updated_at: Optional[datetime]
    last_login: Optional[datetime]
    
    class Config:
        from_attributes = True


class UserProfile(BaseModel):
    id: int
    email: str
    username: str
    full_name: Optional[str]
    avatar_url: Optional[str]
    credits: float
    created_at: datetime
    
    class Config:
        from_attributes = True


class Token(BaseModel):
    access_token: str
    token_type: str = "bearer"
    expires_in: int
    user: UserProfile


class CreditTransactionBase(BaseModel):
    transaction_type: str
    amount: float
    description: Optional[str] = None


class CreditTransaction(CreditTransactionBase):
    id: int
    user_id: int
    balance_after: float
    related_usage_id: Optional[int]
    created_at: datetime
    
    class Config:
        from_attributes = True 