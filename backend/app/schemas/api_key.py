from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime


class APIKeyCreate(BaseModel):
    name: str = Field(..., min_length=1, max_length=100, description="API Key名称")
    description: Optional[str] = Field(None, max_length=500, description="API Key描述")


class APIKeyUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="API Key名称")
    description: Optional[str] = Field(None, max_length=500, description="API Key描述")


class APIKeyResponse(BaseModel):
    id: int
    name: str
    prefix: str
    description: Optional[str]
    usage_count: int
    last_used_at: Optional[datetime]
    created_at: datetime

    class Config:
        from_attributes = True


class APIKeyCreateResponse(BaseModel):
    id: int
    name: str
    key: str  # 完整的API Key，只在创建时返回
    prefix: str
    description: Optional[str]
    created_at: datetime

    class Config:
        from_attributes = True


class APIKeyListResponse(BaseModel):
    success: bool
    data: list[APIKeyResponse] 