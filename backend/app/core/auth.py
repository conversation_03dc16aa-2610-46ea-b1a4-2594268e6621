from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import Optional, Union
from fastapi import Depends, HTTPException, status, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
import jwt

from .config import settings
from .database import get_db
from ..models.user import User
from ..services.api_key_service import APIKeyService


security = HTTPBearer()


def get_current_user_from_jwt(credentials: HTTPAuthorizationCredentials, db: Session) -> User:
    """通过JWT token获取当前用户"""
    try:
        token = credentials.credentials
        payload = jwt.decode(token, settings.JWT_SECRET_KEY, algorithms=[settings.JWT_ALGORITHM])
        user_id: int = payload.get("sub")
        if user_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的认证令牌",
                headers={"WWW-Authenticate": "Bearer"},
            )
    except (jwt.exceptions.JWTException, jwt.exceptions.JWTDecodeError):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的认证令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    user = db.query(User).filter(User.id == user_id).first()
    if user is None:
        raise HTTPException(status_code=404, detail="用户不存在")
    return user


def get_current_user_from_api_key(api_key: str, db: Session) -> User:
    """通过API Key获取当前用户"""
    user = APIKeyService.authenticate_api_key(db, api_key)
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的API Key",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return user


def get_current_user(
    request: Request,
    credentials: HTTPAuthorizationCredentials = Depends(security), 
    db: Session = Depends(get_db)
):
    """获取当前用户 - 支持JWT token和API Key"""
    token = credentials.credentials
    
    # 检查是否是API Key格式
    if token.startswith("sk-"):
        # API Key认证
        return get_current_user_from_api_key(token, db)
    else:
        # JWT token认证
        return get_current_user_from_jwt(credentials, db)


def get_current_user_or_none(
    request: Request,
    db: Session = Depends(get_db)
) -> Optional[User]:
    """获取当前用户，如果未认证则返回None（用于可选认证的端点）"""
    try:
        # 尝试从Authorization header获取token
        auth_header = request.headers.get("authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            return None
        
        token = auth_header.split(" ")[1]
        
        # 检查是否是API Key格式
        if token.startswith("sk-"):
            return APIKeyService.authenticate_api_key(db, token)
        else:
            # JWT token认证
            try:
                payload = jwt.decode(token, settings.JWT_SECRET_KEY, algorithms=[settings.JWT_ALGORITHM])
                user_id: int = payload.get("sub")
                if user_id is None:
                    return None
                    
                user = db.query(User).filter(User.id == user_id).first()
                return user
            except (jwt.exceptions.JWTException, jwt.exceptions.JWTDecodeError):
                return None
                
    except Exception:
        return None


def check_credits_and_deduct(db: Session, user: User, credits_required: float) -> bool:
    """检查积分并扣除"""
    if user.credits < credits_required:
        raise HTTPException(
            status_code=status.HTTP_402_PAYMENT_REQUIRED,
            detail=f"积分不足，需要 {credits_required} 积分，当前余额 {user.credits} 积分"
        )
    
    # 扣除积分
    user.credits -= credits_required
    user.total_credits_used += credits_required
    db.commit()
    
    return True 