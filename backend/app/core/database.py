from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

from .config import settings

# 数据库配置
# MySQL配置
engine = create_engine(
    settings.DATABASE_URL,
    pool_pre_ping=True,
    pool_recycle=3600,  # 1小时回收连接
    pool_size=10,
    max_overflow=20,
    echo=settings.DEBUG  # 开发环境显示SQL语句
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()

# Redis连接 (可选，如果Redis不可用则跳过)
redis_client = None
try:
    import redis
    redis_client = redis.from_url(settings.REDIS_URL, decode_responses=True)
    # 测试连接
    redis_client.ping()
except Exception as e:
    print(f"Redis连接失败，将跳过Redis功能: {e}")
    redis_client = None


def get_db():
    """获取数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def get_redis():
    """获取Redis连接"""
    return redis_client 