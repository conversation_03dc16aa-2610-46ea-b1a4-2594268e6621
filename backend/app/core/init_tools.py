from sqlalchemy.orm import Session
from ..models.api_tool import APITool, APIToolCategory
from .database import engine, get_db


def init_tool_categories(db: Session):
    """初始化工具分类"""
    categories = [
        {
            "name": "Text Processing",
            "icon": "📝",
            "description": "Tools for text processing and transformation"
        },
        {
            "name": "Text Analysis", 
            "icon": "🔍",
            "description": "Tools for text content analysis and detection"
        },
        {
            "name": "Image Generation",
            "icon": "🎨", 
            "description": "AI tools for generating images from text"
        },
        {
            "name": "Data Processing",
            "icon": "📊",
            "description": "Tools for data formatting and parsing"
        }
    ]
    
    for cat_data in categories:
        # 检查分类是否已存在
        existing = db.query(APIToolCategory).filter(APIToolCategory.name == cat_data["name"]).first()
        if not existing:
            category = APIToolCategory(**cat_data)
            db.add(category)
    
    db.commit()


def init_api_tools(db: Session):
    """初始化API工具"""
    tools = [
        {
            "name": "Text Beautifier",
            "slug": "text-beautify",
            "description": "Enhance and improve text expression using AI technology with multiple style transformations",
            "category": "Text Processing",
            "icon": "✨",
            "endpoint": "/ai-tools/text-beautify",
            "method": "POST",
            "cost_per_call": 2,
            "parameters": {
                "type": "object",
                "properties": {
                    "text": {
                        "type": "string",
                        "description": "Text to be beautified",
                        "example": "This product is really good"
                    },
                    "style": {
                        "type": "string",
                        "enum": ["professional", "casual", "formal", "creative"],
                        "default": "professional",
                        "description": "Beautification style"
                    }
                },
                "required": ["text"]
            },
            "example_request": {
                "text": "This product is really good",
                "style": "professional"
            },
            "example_response": {
                "type": "object",
                "properties": {
                    "original_text": {"type": "string", "description": "Original text"},
                    "beautified_text": {"type": "string", "description": "Beautified text"},
                    "style": {"type": "string", "description": "Style used"}
                }
            }
        },
        {
            "name": "Language Detection",
            "slug": "language-detect", 
            "description": "Automatically detect the language type of text with support for multiple languages",
            "category": "Text Analysis",
            "icon": "🌐",
            "endpoint": "/ai-tools/language-detect",
            "method": "POST",
            "cost_per_call": 1,
            "parameters": {
                "type": "object",
                "properties": {
                    "text": {
                        "type": "string",
                        "description": "Text for language detection",
                        "example": "Hello world, this is a test."
                    }
                },
                "required": ["text"]
            },
            "example_request": {
                "text": "Hello world, this is a test."
            },
            "example_response": {
                "type": "object",
                "properties": {
                    "detected_language": {"type": "object", "description": "Detected language information"},
                    "probabilities": {"type": "array", "description": "Language probabilities"}
                }
            }
        },
        {
            "name": "Text to Image",
            "slug": "text-to-image",
            "description": "Generate high-quality images from text descriptions with support for multiple artistic styles", 
            "category": "Image Generation",
            "icon": "🎨",
            "endpoint": "/ai-tools/text-to-image",
            "method": "POST",
            "cost_per_call": 5,
            "parameters": {
                "type": "object",
                "properties": {
                    "text": {
                        "type": "string",
                        "description": "Image description text",
                        "example": "A beautiful sunset over the ocean"
                    },
                    "style": {
                        "type": "string",
                        "enum": ["realistic", "cartoon", "artistic", "abstract"],
                        "default": "realistic",
                        "description": "Image style"
                    },
                    "size": {
                        "type": "string",
                        "enum": ["1024x1024", "512x512", "256x256"],
                        "default": "1024x1024",
                        "description": "Image size"
                    }
                },
                "required": ["text"]
            },
            "example_request": {
                "text": "A beautiful sunset over the ocean",
                "style": "realistic",
                "size": "1024x1024"
            },
            "example_response": {
                "type": "object",
                "properties": {
                    "image_url": {"type": "string", "description": "Generated image URL"},
                    "prompt": {"type": "string", "description": "Used prompt"},
                    "style": {"type": "string", "description": "Image style"},
                    "size": {"type": "string", "description": "Image size"}
                }
            }
        },
        {
            "name": "JSON Parser",
            "slug": "json-parse",
            "description": "Parse and beautify JSON format text with formatting and error checking features",
            "category": "Data Processing", 
            "icon": "📋",
            "endpoint": "/ai-tools/json-parse",
            "method": "POST",
            "cost_per_call": 1,
            "parameters": {
                "type": "object",
                "properties": {
                    "text": {
                        "type": "string",
                        "description": "JSON text to be parsed",
                        "example": '{"name":"John","age":25,"city":"New York"}'
                    },
                    "format_type": {
                        "type": "string",
                        "enum": ["pretty", "compact", "validate"],
                        "default": "pretty",
                        "description": "Format type: pretty(beautify), compact(compact), validate(validate only)"
                    },
                    "indent": {
                        "type": "integer",
                        "minimum": 2,
                        "maximum": 8,
                        "default": 4,
                        "description": "Number of indent spaces"
                    }
                },
                "required": ["text"]
            },
            "example_request": {
                "text": '{"name":"John","age":25,"city":"New York"}',
                "format_type": "pretty",
                "indent": 4
            },
            "example_response": {
                "type": "object",
                "properties": {
                    "original_text": {"type": "string", "description": "Original text"},
                    "parsed_json": {"type": "string", "description": "Parsed JSON text"},
                    "is_valid": {"type": "boolean", "description": "Is JSON valid"},
                    "format_type": {"type": "string", "description": "Used format type"},
                    "error_message": {"type": "string", "description": "Error message (if any)"}
                }
            }
        }
    ]
    
    for tool_data in tools:
        # 检查工具是否已存在
        existing = db.query(APITool).filter(APITool.slug == tool_data["slug"]).first()
        if not existing:
            tool = APITool(**tool_data)
            db.add(tool)
    
    db.commit()


def init_tools_data():
    """初始化工具数据"""
    # 创建数据库会话
    from .database import SessionLocal
    db = SessionLocal()
    
    try:
        print("正在初始化工具分类...")
        init_tool_categories(db)
        print("工具分类初始化完成")
        
        print("正在初始化API工具...")
        init_api_tools(db)
        print("API工具初始化完成")
        
    except Exception as e:
        print(f"初始化失败: {e}")
        db.rollback()
    finally:
        db.close()


if __name__ == "__main__":
    init_tools_data() 