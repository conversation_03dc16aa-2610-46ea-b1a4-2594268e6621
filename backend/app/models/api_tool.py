from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, JSON, Float
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

from ..core.database import Base


class APITool(Base):
    __tablename__ = "api_tools"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    slug = Column(String(100), unique=True, index=True, nullable=False)
    description = Column(Text)
    category = Column(String(50), index=True)
    icon = Column(String(10))
    endpoint = Column(String(200))
    method = Column(String(10), default="POST")
    
    # 使用数据库中实际的字段名
    cost_per_call = Column(Integer, default=1)  # 数据库中是cost_per_call，不是cost_per_request
    
    # JSON字段 - 使用数据库中实际的字段名
    parameters = Column(JSON)  # 数据库中是parameters，相当于input_schema
    example_request = Column(JSON)
    example_response = Column(JSON)  # 相当于output_schema
    
    # 工具状态和统计
    is_active = Column(Boolean, default=True)
    total_calls = Column(Integer, default=0)
    success_rate = Column(Float, default=0.0)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 添加属性来保持API兼容性
    @property
    def cost_per_request(self):
        return float(self.cost_per_call) if self.cost_per_call else 1.0
    
    @property
    def input_schema(self):
        return self.parameters
    
    @property
    def output_schema(self):
        return self.example_response


class APIToolCategory(Base):
    __tablename__ = "api_tool_categories"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), unique=True, nullable=False)
    icon = Column(String(10))
    description = Column(Text)
    is_active = Column(Boolean, default=True)
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())


class APIUsage(Base):
    __tablename__ = "api_usage"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, nullable=False, index=True)  # 外键，关联用户
    tool_id = Column(Integer, nullable=False, index=True)  # 外键，关联API工具
    
    # 请求信息
    request_data = Column(JSON)  # 请求参数
    response_data = Column(JSON)  # 响应数据
    
    # 执行信息
    status = Column(String(20), default="pending")  # pending, success, failed
    error_message = Column(Text)
    execution_time = Column(Float)  # 执行时间（秒）
    
    # 计费信息
    credits_used = Column(Float, default=0.0)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    completed_at = Column(DateTime(timezone=True)) 