from sqlalchemy import Column, Integer, String, Boolean, DateTime, Float, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from ..core.database import Base


class User(Base):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    email = Column(String(255), unique=True, index=True, nullable=False)
    username = Column(String(100), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    
    # 用户信息
    full_name = Column(String(255))
    avatar_url = Column(String(500))
    
    # 账户状态
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)
    
    # 积分信息
    credits = Column(Float, default=0.0)
    total_credits_purchased = Column(Float, default=0.0)
    total_credits_used = Column(Float, default=0.0)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_login = Column(DateTime(timezone=True))

    # 关系
    api_keys = relationship("APIKey", back_populates="user")


class CreditTransaction(Base):
    __tablename__ = "credit_transactions"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, nullable=False, index=True)
    
    # 交易信息
    transaction_type = Column(String(20), nullable=False)  # purchase, usage, refund
    amount = Column(Float, nullable=False)  # 正数为增加，负数为减少
    balance_after = Column(Float, nullable=False)  # 交易后余额
    
    # 描述和关联
    description = Column(Text)
    related_usage_id = Column(Integer)  # 关联的API使用记录ID
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now()) 