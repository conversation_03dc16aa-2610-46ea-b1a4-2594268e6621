from sqlalchemy import Column, Inte<PERSON>, String, <PERSON><PERSON><PERSON>, DateTime, ForeignKey, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import secrets
import string

from ..core.database import Base


class APIKey(Base):
    __tablename__ = "api_keys"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    name = Column(String(100), nullable=False)  # API Key名称，用户自定义
    key_hash = Column(String(255), nullable=False, unique=True, index=True)  # API Key的哈希值
    key_prefix = Column(String(20), nullable=False)  # 显示给用户的前缀，如 "sk-abc..."
    description = Column(Text)  # API Key描述
    is_active = Column(Boolean, default=True)  # 是否激活
    last_used_at = Column(DateTime(timezone=True))  # 最后使用时间
    usage_count = Column(Integer, default=0)  # 使用次数
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 外键关系
    user = relationship("User", back_populates="api_keys")

    @staticmethod
    def generate_api_key() -> tuple[str, str, str]:
        """生成API Key"""
        # 生成32字符的随机字符串
        key_body = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(32))
        full_key = f"sk-{key_body}"
        prefix = f"sk-{key_body[:8]}..."
        
        return full_key, prefix, key_body

    def __repr__(self):
        return f"<APIKey(id={self.id}, name='{self.name}', prefix='{self.key_prefix}')>" 