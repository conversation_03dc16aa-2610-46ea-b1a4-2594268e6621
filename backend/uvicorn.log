INFO:     Will watch for changes in these directories: ['/Users/<USER>/PycharmProjects/aitools/backend']
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     Started reloader process [62618] using WatchFiles
2025-06-01 15:20:17,330 INFO sqlalchemy.engine.Engine SELECT DATABASE()
2025-06-01 15:20:17,331 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-06-01 15:20:17,690 INFO sqlalchemy.engine.Engine SELECT @@sql_mode
2025-06-01 15:20:17,690 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-06-01 15:20:17,870 INFO sqlalchemy.engine.Engine SELECT @@lower_case_table_names
2025-06-01 15:20:17,870 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-06-01 15:20:18,233 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-06-01 15:20:18,234 INFO sqlalchemy.engine.Engine DESCRIBE `aitools`.`api_tools`
2025-06-01 15:20:18,234 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-06-01 15:20:18,413 INFO sqlalchemy.engine.Engine DESCRIBE `aitools`.`api_usage`
2025-06-01 15:20:18,413 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-06-01 15:20:18,596 INFO sqlalchemy.engine.Engine DESCRIBE `aitools`.`users`
2025-06-01 15:20:18,596 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-06-01 15:20:18,777 INFO sqlalchemy.engine.Engine DESCRIBE `aitools`.`credit_transactions`
2025-06-01 15:20:18,777 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-06-01 15:20:18,960 INFO sqlalchemy.engine.Engine DESCRIBE `aitools`.`rate_limits`
2025-06-01 15:20:18,961 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-06-01 15:20:19,158 INFO sqlalchemy.engine.Engine COMMIT
INFO:     Started server process [62621]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
数据库表创建完成
INFO:     127.0.0.1:63570 - "GET /api/v1/ai-tools/categories HTTP/1.1" 200 OK
INFO:     127.0.0.1:63570 - "GET /api/v1/ai-tools/categories HTTP/1.1" 200 OK
INFO:     127.0.0.1:63569 - "GET /api/v1/ai-tools/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:63570 - "GET /api/v1/ai-tools/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:64179 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:65430 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:51042 - "GET /api/v1/ai-tools/categories HTTP/1.1" 200 OK
INFO:     127.0.0.1:51044 - "GET /api/v1/ai-tools/categories HTTP/1.1" 200 OK
INFO:     127.0.0.1:51040 - "GET /api/v1/ai-tools/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:51044 - "GET /api/v1/ai-tools/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:51159 - "GET /api/v1/blog/posts?page=1&limit=10 HTTP/1.1" 200 OK
INFO:     127.0.0.1:51161 - "GET /api/v1/blog/posts?page=1&limit=10 HTTP/1.1" 200 OK
INFO:     127.0.0.1:51214 - "GET /api/v1/user/profile HTTP/1.1" 200 OK
INFO:     127.0.0.1:51216 - "GET /api/v1/user/profile HTTP/1.1" 200 OK
INFO:     127.0.0.1:51343 - "GET /api/v1/user/profile HTTP/1.1" 200 OK
INFO:     127.0.0.1:51378 - "GET /api/v1/user/profile HTTP/1.1" 200 OK
INFO:     127.0.0.1:51442 - "GET /api/v1/user/profile HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'app/api/v1/endpoints/auth.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [62621]
WARNING:  WatchFiles detected changes in 'app/api/v1/endpoints/auth.py'. Reloading...
2025-06-02 21:36:51,179 INFO sqlalchemy.engine.Engine SELECT DATABASE()
2025-06-02 21:36:51,180 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-06-02 21:36:51,702 INFO sqlalchemy.engine.Engine SELECT @@sql_mode
2025-06-02 21:36:51,702 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-06-02 21:36:51,933 INFO sqlalchemy.engine.Engine SELECT @@lower_case_table_names
2025-06-02 21:36:51,934 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-06-02 21:36:52,462 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-06-02 21:36:52,462 INFO sqlalchemy.engine.Engine DESCRIBE `aitools`.`api_tools`
2025-06-02 21:36:52,462 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-06-02 21:36:52,754 INFO sqlalchemy.engine.Engine DESCRIBE `aitools`.`api_usage`
2025-06-02 21:36:52,754 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-06-02 21:36:52,988 INFO sqlalchemy.engine.Engine DESCRIBE `aitools`.`users`
2025-06-02 21:36:52,989 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-06-02 21:36:53,277 INFO sqlalchemy.engine.Engine DESCRIBE `aitools`.`credit_transactions`
2025-06-02 21:36:53,278 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-06-02 21:36:53,512 INFO sqlalchemy.engine.Engine DESCRIBE `aitools`.`rate_limits`
2025-06-02 21:36:53,512 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-06-02 21:36:53,801 INFO sqlalchemy.engine.Engine COMMIT
INFO:     Started server process [65730]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/core/security.py'. Reloading...
2025-06-02 21:36:57,785 INFO sqlalchemy.engine.Engine SELECT DATABASE()
2025-06-02 21:36:57,785 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-06-02 21:36:58,711 INFO sqlalchemy.engine.Engine SELECT @@sql_mode
2025-06-02 21:36:58,711 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-06-02 21:36:58,915 INFO sqlalchemy.engine.Engine SELECT @@lower_case_table_names
2025-06-02 21:36:58,915 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-06-02 21:36:59,326 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-06-02 21:36:59,327 INFO sqlalchemy.engine.Engine DESCRIBE `aitools`.`api_tools`
2025-06-02 21:36:59,327 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-06-02 21:36:59,566 INFO sqlalchemy.engine.Engine DESCRIBE `aitools`.`api_usage`
2025-06-02 21:36:59,567 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-06-02 21:36:59,771 INFO sqlalchemy.engine.Engine DESCRIBE `aitools`.`users`
2025-06-02 21:36:59,771 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-06-02 21:36:59,968 INFO sqlalchemy.engine.Engine DESCRIBE `aitools`.`credit_transactions`
2025-06-02 21:36:59,968 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-06-02 21:37:00,182 INFO sqlalchemy.engine.Engine DESCRIBE `aitools`.`rate_limits`
2025-06-02 21:37:00,183 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-06-02 21:37:00,384 INFO sqlalchemy.engine.Engine COMMIT
INFO:     Started server process [65820]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/core/security.py'. Reloading...
2025-06-02 21:37:06,111 INFO sqlalchemy.engine.Engine SELECT DATABASE()
2025-06-02 21:37:06,111 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-06-02 21:37:06,673 INFO sqlalchemy.engine.Engine SELECT @@sql_mode
2025-06-02 21:37:06,673 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-06-02 21:37:06,980 INFO sqlalchemy.engine.Engine SELECT @@lower_case_table_names
2025-06-02 21:37:06,980 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-06-02 21:37:07,539 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-06-02 21:37:07,539 INFO sqlalchemy.engine.Engine DESCRIBE `aitools`.`api_tools`
2025-06-02 21:37:07,539 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-06-02 21:37:08,376 INFO sqlalchemy.engine.Engine DESCRIBE `aitools`.`api_usage`
2025-06-02 21:37:08,377 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-06-02 21:37:08,603 INFO sqlalchemy.engine.Engine DESCRIBE `aitools`.`users`
2025-06-02 21:37:08,603 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-06-02 21:37:08,826 INFO sqlalchemy.engine.Engine DESCRIBE `aitools`.`credit_transactions`
2025-06-02 21:37:08,826 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-06-02 21:37:09,067 INFO sqlalchemy.engine.Engine DESCRIBE `aitools`.`rate_limits`
2025-06-02 21:37:09,068 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-06-02 21:37:09,301 INFO sqlalchemy.engine.Engine COMMIT
INFO:     Started server process [65870]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/api/v1/api.py'. Reloading...
Process SpawnProcess-5:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/process.py", line 313, in _bootstrap
    self.run()
    ~~~~~~~~^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/aitools/backend/.venv/lib/python3.13/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    ~~~~~~^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/aitools/backend/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 66, in run
    return asyncio.run(self.serve(sockets=sockets))
           ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ~~~~~~~~~~^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/Users/<USER>/PycharmProjects/aitools/backend/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 70, in serve
    await self._serve(sockets)
  File "/Users/<USER>/PycharmProjects/aitools/backend/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 77, in _serve
    config.load()
    ~~~~~~~~~~~^^
  File "/Users/<USER>/PycharmProjects/aitools/backend/.venv/lib/python3.13/site-packages/uvicorn/config.py", line 435, in load
    self.loaded_app = import_from_string(self.app)
                      ~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/aitools/backend/.venv/lib/python3.13/site-packages/uvicorn/importer.py", line 22, in import_from_string
    raise exc from None
  File "/Users/<USER>/PycharmProjects/aitools/backend/.venv/lib/python3.13/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
  File "/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/__init__.py", line 88, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 1026, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/Users/<USER>/PycharmProjects/aitools/backend/app/main.py", line 8, in <module>
    from .api.v1.api import api_router
  File "/Users/<USER>/PycharmProjects/aitools/backend/app/api/v1/api.py", line 3, in <module>
    from .endpoints import ai_tools, auth
  File "/Users/<USER>/PycharmProjects/aitools/backend/app/api/v1/endpoints/auth.py", line 13, in <module>
    import jwt
ModuleNotFoundError: No module named 'jwt'
Process SpawnProcess-6:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/process.py", line 313, in _bootstrap
    self.run()
    ~~~~~~~~^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/aitools/backend/.venv/lib/python3.13/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    ~~~~~~^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/aitools/backend/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 66, in run
    return asyncio.run(self.serve(sockets=sockets))
           ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ~~~~~~~~~~^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/Users/<USER>/PycharmProjects/aitools/backend/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 70, in serve
    await self._serve(sockets)
  File "/Users/<USER>/PycharmProjects/aitools/backend/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 77, in _serve
    config.load()
    ~~~~~~~~~~~^^
  File "/Users/<USER>/PycharmProjects/aitools/backend/.venv/lib/python3.13/site-packages/uvicorn/config.py", line 435, in load
    self.loaded_app = import_from_string(self.app)
                      ~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/aitools/backend/.venv/lib/python3.13/site-packages/uvicorn/importer.py", line 22, in import_from_string
    raise exc from None
  File "/Users/<USER>/PycharmProjects/aitools/backend/.venv/lib/python3.13/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
  File "/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/__init__.py", line 88, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 1026, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/Users/<USER>/PycharmProjects/aitools/backend/app/main.py", line 8, in <module>
    from .api.v1.api import api_router
  File "/Users/<USER>/PycharmProjects/aitools/backend/app/api/v1/api.py", line 3, in <module>
    from .endpoints import ai_tools, auth
  File "/Users/<USER>/PycharmProjects/aitools/backend/app/api/v1/endpoints/auth.py", line 13, in <module>
    import jwt
ModuleNotFoundError: No module named 'jwt'
WARNING:  WatchFiles detected changes in 'app/core/config.py'. Reloading...
Process SpawnProcess-7:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/process.py", line 313, in _bootstrap
    self.run()
    ~~~~~~~~^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/aitools/backend/.venv/lib/python3.13/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    ~~~~~~^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/aitools/backend/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 66, in run
    return asyncio.run(self.serve(sockets=sockets))
           ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ~~~~~~~~~~^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/Users/<USER>/PycharmProjects/aitools/backend/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 70, in serve
    await self._serve(sockets)
  File "/Users/<USER>/PycharmProjects/aitools/backend/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 77, in _serve
    config.load()
    ~~~~~~~~~~~^^
  File "/Users/<USER>/PycharmProjects/aitools/backend/.venv/lib/python3.13/site-packages/uvicorn/config.py", line 435, in load
    self.loaded_app = import_from_string(self.app)
                      ~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/aitools/backend/.venv/lib/python3.13/site-packages/uvicorn/importer.py", line 22, in import_from_string
    raise exc from None
  File "/Users/<USER>/PycharmProjects/aitools/backend/.venv/lib/python3.13/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
  File "/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/__init__.py", line 88, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 1026, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/Users/<USER>/PycharmProjects/aitools/backend/app/main.py", line 8, in <module>
    from .api.v1.api import api_router
  File "/Users/<USER>/PycharmProjects/aitools/backend/app/api/v1/api.py", line 3, in <module>
    from .endpoints import ai_tools, auth
  File "/Users/<USER>/PycharmProjects/aitools/backend/app/api/v1/endpoints/auth.py", line 13, in <module>
    import jwt
ModuleNotFoundError: No module named 'jwt'
WARNING:  WatchFiles detected changes in '.venv/lib/python3.13/site-packages/charset_normalizer/legacy.py', '.venv/lib/python3.13/site-packages/pyparsing/actions.py', '.venv/lib/python3.13/site-packages/cachetools/func.py', '.venv/lib/python3.13/site-packages/requests_oauthlib/compliance_fixes/weibo.py', '.venv/lib/python3.13/site-packages/oauthlib/oauth2/rfc6749/utils.py', '.venv/lib/python3.13/site-packages/requests/adapters.py', '.venv/lib/python3.13/site-packages/urllib3/contrib/__init__.py', '.venv/lib/python3.13/site-packages/google/auth/api_key.py', '.venv/lib/python3.13/site-packages/requests/models.py', '.venv/lib/python3.13/site-packages/urllib3/_collections.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc4210.py', '.venv/lib/python3.13/site-packages/requests/certs.py', '.venv/lib/python3.13/site-packages/oauthlib/oauth1/rfc5849/parameters.py', '.venv/lib/python3.13/site-packages/oauthlib/oauth2/rfc6749/clients/__init__.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc8708.py', '.venv/lib/python3.13/site-packages/httplib2/certs.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc8619.py', '.venv/lib/python3.13/site-packages/google/auth/crypt/base.py', '.venv/lib/python3.13/site-packages/google/auth/_exponential_backoff.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc5480.py', '.venv/lib/python3.13/site-packages/urllib3/util/connection.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc3161.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc6170.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc5275.py', '.venv/lib/python3.13/site-packages/google_auth_oauthlib/helpers.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc3770.py', '.venv/lib/python3.13/site-packages/google/auth/__init__.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc7585.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc3370.py', '.venv/lib/python3.13/site-packages/requests/compat.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc3537.py', '.venv/lib/python3.13/site-packages/google/auth/exceptions.py', '.venv/lib/python3.13/site-packages/urllib3/util/url.py', '.venv/lib/python3.13/site-packages/google/auth/_default.py', '.venv/lib/python3.13/site-packages/httplib2/__init__.py', '.venv/lib/python3.13/site-packages/oauthlib/oauth1/rfc5849/errors.py', '.venv/lib/python3.13/site-packages/google/auth/version.py', '.venv/lib/python3.13/site-packages/google/oauth2/_id_token_async.py', '.venv/lib/python3.13/site-packages/oauthlib/oauth1/rfc5849/request_validator.py', '.venv/lib/python3.13/site-packages/oauthlib/openid/connect/__init__.py', '.venv/lib/python3.13/site-packages/charset_normalizer/constant.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/__init__.py', '.venv/lib/python3.13/site-packages/oauthlib/openid/connect/core/tokens.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc5958.py', '.venv/lib/python3.13/site-packages/oauthlib/openid/connect/core/grant_types/dispatchers.py', '.venv/lib/python3.13/site-packages/google/auth/_default_async.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc8358.py', '.venv/lib/python3.13/site-packages/urllib3/_version.py', '.venv/lib/python3.13/site-packages/oauthlib/openid/connect/core/grant_types/refresh_token.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc8018.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc5924.py', '.venv/lib/python3.13/site-packages/requests_oauthlib/compliance_fixes/douban.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc8494.py', '.venv/lib/python3.13/site-packages/oauthlib/oauth1/rfc5849/endpoints/__init__.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc8692.py', '.venv/lib/python3.13/site-packages/pyparsing/common.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc4491.py', '.venv/lib/python3.13/site-packages/requests_oauthlib/compliance_fixes/mailchimp.py', '.venv/lib/python3.13/site-packages/requests_oauthlib/compliance_fixes/__init__.py', '.venv/lib/python3.13/site-packages/google/auth/aio/credentials.py', '.venv/lib/python3.13/site-packages/oauthlib/oauth2/rfc6749/grant_types/client_credentials.py', '.venv/lib/python3.13/site-packages/oauthlib/oauth1/rfc5849/endpoints/request_token.py', '.venv/lib/python3.13/site-packages/requests/api.py', '.venv/lib/python3.13/site-packages/google_auth_oauthlib/__init__.py', '.venv/lib/python3.13/site-packages/google/oauth2/service_account.py', '.venv/lib/python3.13/site-packages/google/auth/iam.py', '.venv/lib/python3.13/site-packages/google/oauth2/_service_account_async.py', '.venv/lib/python3.13/site-packages/google_auth_oauthlib/flow.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc8226.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc5639.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc1901.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc5035.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc8696.py', '.venv/lib/python3.13/site-packages/requests/__init__.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc2314.py', '.venv/lib/python3.13/site-packages/urllib3/poolmanager.py', '.venv/lib/python3.13/site-packages/urllib3/http2/connection.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc3709.py', '.venv/lib/python3.13/site-packages/oauthlib/oauth2/rfc6749/endpoints/resource.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc3114.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc2251.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc8479.py', '.venv/lib/python3.13/site-packages/charset_normalizer/__init__.py', '.venv/lib/python3.13/site-packages/requests_oauthlib/__init__.py', '.venv/lib/python3.13/site-packages/urllib3/filepost.py', '.venv/lib/python3.13/site-packages/requests_oauthlib/compliance_fixes/facebook.py', '.venv/lib/python3.13/site-packages/requests/cookies.py', '.venv/lib/python3.13/site-packages/oauthlib/common.py', '.venv/lib/python3.13/site-packages/pyparsing/util.py', '.venv/lib/python3.13/site-packages/oauthlib/oauth1/rfc5849/endpoints/resource.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc3447.py', '.venv/lib/python3.13/site-packages/google/auth/credentials.py', '.venv/lib/python3.13/site-packages/charset_normalizer/cd.py', '.venv/lib/python3.13/site-packages/urllib3/fields.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc4357.py', '.venv/lib/python3.13/site-packages/oauthlib/oauth2/rfc8628/clients/__init__.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc5280.py', '.venv/lib/python3.13/site-packages/requests_oauthlib/oauth1_session.py', '.venv/lib/python3.13/site-packages/google/oauth2/_client.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc7508.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc2985.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc7773.py', '.venv/lib/python3.13/site-packages/oauthlib/oauth2/rfc6749/endpoints/metadata.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc8209.py', '.venv/lib/python3.13/site-packages/oauthlib/oauth1/rfc5849/__init__.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc2459.py', '.venv/lib/python3.13/site-packages/oauthlib/oauth2/rfc6749/endpoints/base.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc5652.py', '.venv/lib/python3.13/site-packages/google/auth/app_engine.py', '.venv/lib/python3.13/site-packages/google/auth/pluggable.py', '.venv/lib/python3.13/site-packages/oauthlib/oauth2/rfc8628/clients/device.py', '.venv/lib/python3.13/site-packages/urllib3/exceptions.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc3739.py', '.venv/lib/python3.13/site-packages/oauthlib/openid/connect/core/exceptions.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc1157.py', '.venv/lib/python3.13/site-packages/urllib3/util/response.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc3412.py', '.venv/lib/python3.13/site-packages/charset_normalizer/models.py', '.venv/lib/python3.13/site-packages/urllib3/contrib/socks.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc1902.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc7030.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc6487.py', '.venv/lib/python3.13/site-packages/oauthlib/oauth2/rfc6749/endpoints/pre_configured.py', '.venv/lib/python3.13/site-packages/oauthlib/oauth2/rfc6749/errors.py', '.venv/lib/python3.13/site-packages/google/auth/aio/__init__.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc3281.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc5752.py', '.venv/lib/python3.13/site-packages/urllib3/http2/__init__.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc8360.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc8418.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc6955.py', '.venv/lib/python3.13/site-packages/google/auth/_refresh_worker.py', '.venv/lib/python3.13/site-packages/oauthlib/openid/connect/core/endpoints/pre_configured.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc2876.py', '.venv/lib/python3.13/site-packages/google/auth/downscoped.py', '.venv/lib/python3.13/site-packages/requests/__version__.py', '.venv/lib/python3.13/site-packages/urllib3/connection.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc7191.py', '.venv/lib/python3.13/site-packages/urllib3/contrib/pyopenssl.py', '.venv/lib/python3.13/site-packages/urllib3/util/util.py', '.venv/lib/python3.13/site-packages/requests/help.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc4985.py', '.venv/lib/python3.13/site-packages/oauthlib/oauth1/rfc5849/endpoints/base.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc6032.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc1905.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc5913.py', '.venv/lib/python3.13/site-packages/google/oauth2/credentials.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc7906.py', '.venv/lib/python3.13/site-packages/google/auth/_credentials_base.py', '.venv/lib/python3.13/site-packages/requests_oauthlib/compliance_fixes/fitbit.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc6211.py', '.venv/lib/python3.13/site-packages/cachetools/keys.py', '.venv/lib/python3.13/site-packages/oauthlib/oauth1/rfc5849/endpoints/pre_configured.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc8769.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc3414.py', '.venv/lib/python3.13/site-packages/oauthlib/openid/__init__.py', '.venv/lib/python3.13/site-packages/charset_normalizer/utils.py', '.venv/lib/python3.13/site-packages/google/auth/transport/grpc.py', '.venv/lib/python3.13/site-packages/urllib3/contrib/emscripten/request.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc3280.py', '.venv/lib/python3.13/site-packages/google/auth/aws.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc7296.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc4387.py', '.venv/lib/python3.13/site-packages/oauthlib/oauth2/rfc6749/request_validator.py', '.venv/lib/python3.13/site-packages/urllib3/contrib/emscripten/__init__.py', '.venv/lib/python3.13/site-packages/google/auth/transport/_aiohttp_requests.py', '.venv/lib/python3.13/site-packages/google/oauth2/_credentials_async.py', '.venv/lib/python3.13/site-packages/oauthlib/__init__.py', '.venv/lib/python3.13/site-packages/google/auth/_service_account_info.py', '.venv/lib/python3.13/site-packages/oauthlib/openid/connect/core/grant_types/implicit.py', '.venv/lib/python3.13/site-packages/google/oauth2/challenges.py', '.venv/lib/python3.13/site-packages/oauthlib/oauth2/rfc6749/grant_types/implicit.py', '.venv/lib/python3.13/site-packages/oauthlib/oauth2/rfc6749/grant_types/base.py', '.venv/lib/python3.13/site-packages/httplib2/error.py', '.venv/lib/python3.13/site-packages/oauthlib/oauth2/rfc6749/clients/mobile_application.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc5940.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc3852.py', '.venv/lib/python3.13/site-packages/google/auth/external_account_authorized_user.py', '.venv/lib/python3.13/site-packages/google/auth/impersonated_credentials.py', '.venv/lib/python3.13/site-packages/oauthlib/oauth2/rfc6749/tokens.py', '.venv/lib/python3.13/site-packages/google/auth/crypt/_cryptography_rsa.py', '.venv/lib/python3.13/site-packages/google/oauth2/utils.py', '.venv/lib/python3.13/site-packages/httplib2/iri2uri.py', '.venv/lib/python3.13/site-packages/httplib2/socks.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc2437.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc3779.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc3125.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc5636.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc6120.py', '.venv/lib/python3.13/site-packages/requests/sessions.py', '.venv/lib/python3.13/site-packages/oauthlib/oauth2/rfc6749/parameters.py', '.venv/lib/python3.13/site-packages/oauthlib/signals.py', '.venv/lib/python3.13/site-packages/urllib3/util/__init__.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc2315.py', '.venv/lib/python3.13/site-packages/requests_oauthlib/compliance_fixes/instagram.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc6010.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc1155.py', '.venv/lib/python3.13/site-packages/google/auth/transport/urllib3.py', '.venv/lib/python3.13/site-packages/google/oauth2/_reauth_async.py', '.venv/lib/python3.13/site-packages/charset_normalizer/cli/__main__.py', '.venv/lib/python3.13/site-packages/google/auth/aio/transport/aiohttp.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc5915.py', '.venv/lib/python3.13/site-packages/urllib3/http2/probe.py', '.venv/lib/python3.13/site-packages/oauthlib/oauth2/rfc6749/clients/legacy_application.py', '.venv/lib/python3.13/site-packages/urllib3/util/timeout.py', '.venv/lib/python3.13/site-packages/google/auth/transport/_custom_tls_signer.py', '.venv/lib/python3.13/site-packages/charset_normalizer/version.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc6187.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc4108.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc6482.py', '.venv/lib/python3.13/site-packages/google/auth/jwt.py', '.venv/lib/python3.13/site-packages/pyparsing/results.py', '.venv/lib/python3.13/site-packages/google/auth/_jwt_async.py', '.venv/lib/python3.13/site-packages/google/auth/crypt/es256.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc5084.py', '.venv/lib/python3.13/site-packages/requests_oauthlib/compliance_fixes/plentymarkets.py', '.venv/lib/python3.13/site-packages/cachetools/_decorators.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc3565.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc5126.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc7914.py', '.venv/lib/python3.13/site-packages/google/auth/transport/mtls.py', '.venv/lib/python3.13/site-packages/google/oauth2/gdch_credentials.py', '.venv/lib/python3.13/site-packages/google/auth/identity_pool.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc8410.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc7633.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc7292.py', '.venv/lib/python3.13/site-packages/oauthlib/openid/connect/core/endpoints/__init__.py', '.venv/lib/python3.13/site-packages/requests/packages.py', '.venv/lib/python3.13/site-packages/requests/structures.py', '.venv/lib/python3.13/site-packages/google/oauth2/__init__.py', '.venv/lib/python3.13/site-packages/urllib3/util/wait.py', '.venv/lib/python3.13/site-packages/pyparsing/tools/__init__.py', '.venv/lib/python3.13/site-packages/oauthlib/openid/connect/core/request_validator.py', '.venv/lib/python3.13/site-packages/pyparsing/diagram/__init__.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc8702.py', '.venv/lib/python3.13/site-packages/requests_oauthlib/oauth2_session.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc6664.py', '.venv/lib/python3.13/site-packages/google/auth/crypt/_helpers.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc4476.py', '.venv/lib/python3.13/site-packages/oauthlib/openid/connect/core/grant_types/base.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc5755.py', '.venv/lib/python3.13/site-packages/oauthlib/oauth2/rfc6749/grant_types/refresh_token.py', '.venv/lib/python3.13/site-packages/oauthlib/oauth2/rfc6749/endpoints/__init__.py', '.venv/lib/python3.13/site-packages/pyparsing/core.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc8419.py', '.venv/lib/python3.13/site-packages/requests_oauthlib/compliance_fixes/ebay.py', '.venv/lib/python3.13/site-packages/requests/status_codes.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc3274.py', '.venv/lib/python3.13/site-packages/requests/auth.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc4055.py', '.venv/lib/python3.13/site-packages/oauthlib/oauth1/__init__.py', '.venv/lib/python3.13/site-packages/google/auth/compute_engine/__init__.py', '.venv/lib/python3.13/site-packages/oauthlib/oauth2/rfc6749/clients/base.py', '.venv/lib/python3.13/site-packages/google_auth_oauthlib/tool/__init__.py', '.venv/lib/python3.13/site-packages/oauthlib/oauth2/rfc6749/grant_types/resource_owner_password_credentials.py', '.venv/lib/python3.13/site-packages/oauthlib/oauth2/rfc6749/clients/backend_application.py', '.venv/lib/python3.13/site-packages/google_auth_oauthlib/interactive.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc6019.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc6960.py', '.venv/lib/python3.13/site-packages/google/auth/external_account.py', '.venv/lib/python3.13/site-packages/google/auth/aio/transport/__init__.py', '.venv/lib/python3.13/site-packages/requests_oauthlib/oauth1_auth.py', '.venv/lib/python3.13/site-packages/oauthlib/oauth1/rfc5849/utils.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc3279.py', '.venv/lib/python3.13/site-packages/httplib2/auth.py', '.venv/lib/python3.13/site-packages/requests/exceptions.py', '.venv/lib/python3.13/site-packages/urllib3/_base_connection.py', '.venv/lib/python3.13/site-packages/google/auth/_oauth2client.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc5697.py', '.venv/lib/python3.13/site-packages/google/auth/crypt/__init__.py', '.venv/lib/python3.13/site-packages/requests/hooks.py', '.venv/lib/python3.13/site-packages/oauthlib/oauth1/rfc5849/endpoints/signature_only.py', '.venv/lib/python3.13/site-packages/google/oauth2/reauth.py', '.venv/lib/python3.13/site-packages/oauthlib/oauth2/rfc6749/clients/web_application.py', '.venv/lib/python3.13/site-packages/charset_normalizer/md.py', '.venv/lib/python3.13/site-packages/google/oauth2/webauthn_handler_factory.py', '.venv/lib/python3.13/site-packages/google/auth/compute_engine/_metadata.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc5649.py', '.venv/lib/python3.13/site-packages/google_auth_oauthlib/tool/__main__.py', '.venv/lib/python3.13/site-packages/urllib3/util/ssltransport.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc5914.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc8520.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc2986.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc6486.py', '.venv/lib/python3.13/site-packages/urllib3/__init__.py', '.venv/lib/python3.13/site-packages/google/auth/transport/requests.py', '.venv/lib/python3.13/site-packages/oauthlib/oauth2/rfc6749/endpoints/token.py', '.venv/lib/python3.13/site-packages/pyparsing/unicode.py', '.venv/lib/python3.13/site-packages/google/auth/aio/_helpers.py', '.venv/lib/python3.13/site-packages/google/oauth2/id_token.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc3820.py', '.venv/lib/python3.13/site-packages/urllib3/util/ssl_.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc5083.py', '.venv/lib/python3.13/site-packages/oauthlib/oauth1/rfc5849/signature.py', '.venv/lib/python3.13/site-packages/oauthlib/oauth2/rfc6749/__init__.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc4010.py', '.venv/lib/python3.13/site-packages/requests_oauthlib/compliance_fixes/slack.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc4043.py', '.venv/lib/python3.13/site-packages/pyparsing/tools/cvt_pyparsing_pep8_names.py', '.venv/lib/python3.13/site-packages/urllib3/util/ssl_match_hostname.py', '.venv/lib/python3.13/site-packages/oauthlib/oauth1/rfc5849/endpoints/authorization.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc3560.py', '.venv/lib/python3.13/site-packages/urllib3/util/retry.py', '.venv/lib/python3.13/site-packages/urllib3/response.py', '.venv/lib/python3.13/site-packages/pyparsing/helpers.py', '.venv/lib/python3.13/site-packages/requests/_internal_utils.py', '.venv/lib/python3.13/site-packages/google/oauth2/_client_async.py', '.venv/lib/python3.13/site-packages/urllib3/util/proxy.py', '.venv/lib/python3.13/site-packages/oauthlib/oauth1/rfc5849/endpoints/access_token.py', '.venv/lib/python3.13/site-packages/oauthlib/openid/connect/core/grant_types/hybrid.py', '.venv/lib/python3.13/site-packages/oauthlib/oauth2/rfc6749/clients/service_application.py', '.venv/lib/python3.13/site-packages/requests/utils.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc3058.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc4211.py', '.venv/lib/python3.13/site-packages/charset_normalizer/api.py', '.venv/lib/python3.13/site-packages/google/auth/crypt/_python_rsa.py', '.venv/lib/python3.13/site-packages/oauthlib/openid/connect/core/grant_types/__init__.py', '.venv/lib/python3.13/site-packages/urllib3/_request_methods.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc2511.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc2560.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc5990.py', '.venv/lib/python3.13/site-packages/urllib3/connectionpool.py', '.venv/lib/python3.13/site-packages/urllib3/util/request.py', '.venv/lib/python3.13/site-packages/oauthlib/oauth2/rfc6749/endpoints/authorization.py', '.venv/lib/python3.13/site-packages/charset_normalizer/cli/__init__.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc8017.py', '.venv/lib/python3.13/site-packages/google/auth/transport/_requests_base.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc6210.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc2634.py', '.venv/lib/python3.13/site-packages/google/auth/compute_engine/credentials.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc4334.py', '.venv/lib/python3.13/site-packages/urllib3/contrib/emscripten/response.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc5916.py', '.venv/lib/python3.13/site-packages/pyparsing/exceptions.py', '.venv/lib/python3.13/site-packages/oauthlib/oauth2/rfc6749/grant_types/authorization_code.py', '.venv/lib/python3.13/site-packages/pyparsing/__init__.py', '.venv/lib/python3.13/site-packages/google/auth/aio/transport/sessions.py', '.venv/lib/python3.13/site-packages/google/auth/metrics.py', '.venv/lib/python3.13/site-packages/oauthlib/oauth2/rfc6749/endpoints/revocation.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc7229.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc5934.py', '.venv/lib/python3.13/site-packages/google/auth/environment_vars.py', '.venv/lib/python3.13/site-packages/google/oauth2/webauthn_types.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc8398.py', '.venv/lib/python3.13/site-packages/oauthlib/uri_validate.py', '.venv/lib/python3.13/site-packages/google/auth/transport/_mtls_helper.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc8103.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/pem.py', '.venv/lib/python3.13/site-packages/google/oauth2/webauthn_handler.py', '.venv/lib/python3.13/site-packages/oauthlib/oauth2/rfc6749/grant_types/__init__.py', '.venv/lib/python3.13/site-packages/cachetools/__init__.py', '.venv/lib/python3.13/site-packages/oauthlib/openid/connect/core/grant_types/authorization_code.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc8649.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc2631.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc7894.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc4073.py', '.venv/lib/python3.13/site-packages/google/auth/_credentials_async.py', '.venv/lib/python3.13/site-packages/charset_normalizer/__main__.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc5917.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc5753.py', '.venv/lib/python3.13/site-packages/oauthlib/oauth2/rfc6749/endpoints/introspect.py', '.venv/lib/python3.13/site-packages/google/auth/_helpers.py', '.venv/lib/python3.13/site-packages/oauthlib/openid/connect/core/endpoints/userinfo.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc3657.py', '.venv/lib/python3.13/site-packages/oauthlib/oauth2/rfc8628/__init__.py', '.venv/lib/python3.13/site-packages/requests_oauthlib/oauth2_auth.py', '.venv/lib/python3.13/site-packages/google/oauth2/sts.py', '.venv/lib/python3.13/site-packages/oauthlib/oauth2/__init__.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc4490.py', '.venv/lib/python3.13/site-packages/google/auth/transport/_http_client.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc5208.py', '.venv/lib/python3.13/site-packages/pyparsing/testing.py', '.venv/lib/python3.13/site-packages/google_auth_httplib2.py', '.venv/lib/python3.13/site-packages/google/auth/crypt/rsa.py', '.venv/lib/python3.13/site-packages/urllib3/contrib/emscripten/fetch.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc6402.py', '.venv/lib/python3.13/site-packages/urllib3/contrib/emscripten/connection.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc6031.py', '.venv/lib/python3.13/site-packages/oauthlib/openid/connect/core/__init__.py', '.venv/lib/python3.13/site-packages/google/auth/_cloud_sdk.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc4683.py', '.venv/lib/python3.13/site-packages/pyasn1_modules/rfc5751.py', '.venv/lib/python3.13/site-packages/google/auth/transport/__init__.py'. Reloading...
Process SpawnProcess-8:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/process.py", line 313, in _bootstrap
    self.run()
    ~~~~~~~~^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/aitools/backend/.venv/lib/python3.13/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    ~~~~~~^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/aitools/backend/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 66, in run
    return asyncio.run(self.serve(sockets=sockets))
           ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ~~~~~~~~~~^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/Users/<USER>/PycharmProjects/aitools/backend/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 70, in serve
    await self._serve(sockets)
  File "/Users/<USER>/PycharmProjects/aitools/backend/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 77, in _serve
    config.load()
    ~~~~~~~~~~~^^
  File "/Users/<USER>/PycharmProjects/aitools/backend/.venv/lib/python3.13/site-packages/uvicorn/config.py", line 435, in load
    self.loaded_app = import_from_string(self.app)
                      ~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/aitools/backend/.venv/lib/python3.13/site-packages/uvicorn/importer.py", line 22, in import_from_string
    raise exc from None
  File "/Users/<USER>/PycharmProjects/aitools/backend/.venv/lib/python3.13/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
  File "/opt/homebrew/Cellar/python@3.13/3.13.3_1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/__init__.py", line 88, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 1026, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/Users/<USER>/PycharmProjects/aitools/backend/app/main.py", line 8, in <module>
    from .api.v1.api import api_router
  File "/Users/<USER>/PycharmProjects/aitools/backend/app/api/v1/api.py", line 3, in <module>
    from .endpoints import ai_tools, auth
  File "/Users/<USER>/PycharmProjects/aitools/backend/app/api/v1/endpoints/auth.py", line 13, in <module>
    import jwt
ModuleNotFoundError: No module named 'jwt'
INFO:     Stopping reloader process [62618]
