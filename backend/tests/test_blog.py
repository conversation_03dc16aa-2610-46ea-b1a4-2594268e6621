import pytest
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)

class TestBlogAPI:
    """博客API测试"""
    
    def test_get_blog_posts_success(self):
        """测试获取博客文章列表成功"""
        response = client.get("/api/v1/blog/posts")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "posts" in data
        assert "total" in data
        assert "page" in data
        assert "limit" in data
        assert isinstance(data["posts"], list)
        assert data["page"] == 1
        assert data["limit"] == 10
    
    def test_get_blog_posts_with_pagination(self):
        """测试分页获取博客文章"""
        response = client.get("/api/v1/blog/posts?page=1&limit=2")
        
        assert response.status_code == 200
        data = response.json()
        
        assert len(data["posts"]) <= 2
        assert data["page"] == 1
        assert data["limit"] == 2
    
    def test_get_blog_post_by_slug_success(self):
        """测试根据slug获取博客文章成功"""
        response = client.get("/api/v1/blog/posts/ai-tools-guide")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["slug"] == "ai-tools-guide"
        assert data["title"] == "AI工具使用指南"
        assert "content" in data
        assert "author" in data
        assert "tags" in data
    
    def test_get_blog_post_not_found(self):
        """测试获取不存在的博客文章"""
        response = client.get("/api/v1/blog/posts/non-existent-slug")
        
        assert response.status_code == 404
        data = response.json()
        assert "文章不存在" in data["detail"]
    
    def test_blog_post_structure(self):
        """测试博客文章数据结构"""
        response = client.get("/api/v1/blog/posts")
        
        assert response.status_code == 200
        data = response.json()
        
        if data["posts"]:
            post = data["posts"][0]
            required_fields = ["id", "title", "slug", "excerpt", "content", 
                             "author", "published_at", "tags", "read_time"]
            
            for field in required_fields:
                assert field in post
            
            assert isinstance(post["tags"], list)
            assert isinstance(post["read_time"], int)
    
    def test_invalid_pagination_parameters(self):
        """测试无效的分页参数"""
        # 测试负数页码
        response = client.get("/api/v1/blog/posts?page=0")
        assert response.status_code == 422
        
        # 测试过大的limit
        response = client.get("/api/v1/blog/posts?limit=100")
        assert response.status_code == 422 