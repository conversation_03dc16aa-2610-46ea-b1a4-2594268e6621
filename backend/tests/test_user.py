import pytest
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)

class TestUserAPI:
    """用户API测试"""
    
    def test_get_user_profile_success(self):
        """测试获取用户资料成功"""
        response = client.get("/api/v1/user/profile")
        
        assert response.status_code == 200
        data = response.json()
        
        required_fields = ["id", "username", "email", "credits", 
                          "subscription", "usage_stats", "created_at"]
        
        for field in required_fields:
            assert field in data
        
        # 检查订阅信息结构
        subscription = data["subscription"]
        assert "plan" in subscription
        assert "expires_at" in subscription
        assert "status" in subscription
        
        # 检查使用统计结构
        usage_stats = data["usage_stats"]
        assert "total_requests" in usage_stats
        assert "this_month_requests" in usage_stats
        assert "favorite_tool" in usage_stats
    
    def test_get_user_credits_success(self):
        """测试获取用户积分信息成功"""
        response = client.get("/api/v1/user/credits")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "credits" in data
        assert "subscription_plan" in data
        assert "subscription_status" in data
        assert isinstance(data["credits"], int)
    
    def test_get_usage_stats_success(self):
        """测试获取使用统计成功"""
        response = client.get("/api/v1/user/usage-stats")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "total_requests" in data
        assert "this_month_requests" in data
        assert "favorite_tool" in data
        assert isinstance(data["total_requests"], int)
        assert isinstance(data["this_month_requests"], int)
    
    def test_recharge_credits_success(self):
        """测试充值积分成功"""
        response = client.post("/api/v1/user/credits/recharge?amount=100")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["success"] is True
        assert "message" in data
        assert "new_balance" in data
        assert isinstance(data["new_balance"], int)
    
    def test_recharge_credits_invalid_amount(self):
        """测试充值无效金额"""
        response = client.post("/api/v1/user/credits/recharge?amount=0")
        
        assert response.status_code == 400
        data = response.json()
        assert "充值金额必须大于0" in data["detail"]
        
        response = client.post("/api/v1/user/credits/recharge?amount=-10")
        
        assert response.status_code == 400
        data = response.json()
        assert "充值金额必须大于0" in data["detail"]
    
    def test_get_user_activities_success(self):
        """测试获取用户活动记录成功"""
        response = client.get("/api/v1/user/activities")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "activities" in data
        assert "total" in data
        assert isinstance(data["activities"], list)
        assert isinstance(data["total"], int)
        
        if data["activities"]:
            activity = data["activities"][0]
            required_fields = ["id", "type", "description", "details", "timestamp"]
            
            for field in required_fields:
                assert field in activity
    
    def test_user_data_types(self):
        """测试用户数据类型正确性"""
        response = client.get("/api/v1/user/profile")
        
        assert response.status_code == 200
        data = response.json()
        
        assert isinstance(data["id"], int)
        assert isinstance(data["username"], str)
        assert isinstance(data["email"], str)
        assert isinstance(data["credits"], int)
        assert isinstance(data["subscription"]["plan"], str)
        assert isinstance(data["subscription"]["status"], str)
        assert isinstance(data["usage_stats"]["total_requests"], int)
        assert isinstance(data["usage_stats"]["this_month_requests"], int) 