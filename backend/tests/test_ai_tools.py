import pytest
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)


def test_get_api_tools():
    """测试获取API工具列表"""
    response = client.get("/api/v1/ai-tools/")
    assert response.status_code == 200
    data = response.json()
    assert "tools" in data
    assert "total" in data
    assert len(data["tools"]) > 0


def test_get_tool_categories():
    """测试获取工具分类"""
    response = client.get("/api/v1/ai-tools/categories")
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "data" in data
    assert len(data["data"]) > 0


def test_text_beautify():
    """测试文本美化API"""
    test_data = {
        "text": "这是一个测试文本",
        "style": "professional"
    }
    response = client.post("/api/v1/ai-tools/text-beautify", json=test_data)
    
    # 注意：这个测试可能会失败，因为需要真实的OpenRouter API密钥
    # 在实际测试中，应该使用mock来模拟API调用
    if response.status_code == 200:
        data = response.json()
        assert data["success"] is True
        assert "data" in data
        assert data["credits_used"] == 2.0
    else:
        # 如果API密钥未配置，应该返回500错误
        assert response.status_code in [400, 500]


def test_language_detect():
    """测试语言检测API"""
    test_data = {
        "text": "Hello, this is a test text in English."
    }
    response = client.post("/api/v1/ai-tools/language-detect", json=test_data)
    
    if response.status_code == 200:
        data = response.json()
        assert data["success"] is True
        assert "data" in data
        assert data["credits_used"] == 1.0
    else:
        assert response.status_code in [400, 500]


def test_text_to_image():
    """测试文本生成图片API"""
    test_data = {
        "text": "A beautiful sunset over the ocean",
        "style": "realistic",
        "size": "512x512"
    }
    response = client.post("/api/v1/ai-tools/text-to-image", json=test_data)
    
    if response.status_code == 200:
        data = response.json()
        assert data["success"] is True
        assert "data" in data
        assert data["credits_used"] == 5.0
    else:
        assert response.status_code in [400, 500]


def test_health_check():
    """测试健康检查端点"""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"


def test_root_endpoint():
    """测试根端点"""
    response = client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert "message" in data
    assert "version" in data 