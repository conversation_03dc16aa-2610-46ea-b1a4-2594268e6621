# Token计数API文档

## 概述

Token计数API工具可以帮助用户计算文本在不同AI模型中占用的token数量，并提供成本估算。支持包括GPT-4、GPT-3.5-turbo、<PERSON>等主流AI模型。

## API端点

```
POST /api/v1/ai-tools/token-count
```

## 请求参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| text | string | 是 | - | 需要计算token的文本 |
| model | string | 否 | "gpt-4" | AI模型名称 |

### 支持的模型

- `gpt-4`: GPT-4模型
- `gpt-4-turbo`: GPT-4 Turbo模型
- `gpt-3.5-turbo`: GPT-3.5 Turbo模型
- `gpt-3.5-turbo-16k`: GPT-3.5 Turbo 16K模型
- `claude-3`: <PERSON> 3模型
- `claude-3-opus`: <PERSON> 3 Opus模型
- `claude-3-sonnet`: <PERSON> 3 Sonnet模型
- `claude-3-haiku`: <PERSON> 3 Haiku模型

## 请求示例

### 基本用法

```bash
curl -X POST "http://localhost:8000/api/v1/ai-tools/token-count" \
     -H "Content-Type: application/json" \
     -d '{
       "text": "Hello, how are you today? This is a sample text for token counting.",
       "model": "gpt-4"
     }'
```

### 中文文本示例

```bash
curl -X POST "http://localhost:8000/api/v1/ai-tools/token-count" \
     -H "Content-Type: application/json" \
     -d '{
       "text": "这是一段中文测试文本，用来测试不同模型的token计算功能。",
       "model": "gpt-3.5-turbo"
     }'
```

### Claude模型示例

```bash
curl -X POST "http://localhost:8000/api/v1/ai-tools/token-count" \
     -H "Content-Type: application/json" \
     -d '{
       "text": "Mixed language text: 这里是中英文混合的内容，用于测试token计算。",
       "model": "claude-3-sonnet"
     }'
```

## 响应格式

### 成功响应

```json
{
  "success": true,
  "data": {
    "text": "Hello, how are you today? This is a sample text for token counting.",
    "text_length": 67,
    "model": "gpt-4",
    "token_count": 16,
    "encoding_model": "gpt-4",
    "estimated_cost_usd": 0.00048,
    "cost_per_1k_tokens": 0.03,
    "token_to_char_ratio": 0.239,
    "execution_time": 0.001234
  },
  "error": null,
  "credits_used": 0.5,
  "execution_time": 0.001234
}
```

### 响应字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| text | string | 输入的文本（如果超过100字符会截断显示） |
| text_length | integer | 文本字符数 |
| model | string | 使用的模型名称 |
| token_count | integer | 计算出的token数量 |
| encoding_model | string | 实际使用的编码器模型 |
| estimated_cost_usd | float | 估算的成本（美元） |
| cost_per_1k_tokens | float | 每千token的成本 |
| token_to_char_ratio | float | token与字符的比率 |
| execution_time | float | 执行时间（秒） |

### 错误响应

```json
{
  "success": false,
  "data": null,
  "error": "Token计数失败: 具体错误信息",
  "credits_used": 0.0,
  "execution_time": 0.0
}
```

## 使用成本

- **登录用户**: 每次调用消耗0.5积分
- **未登录用户**: 免费使用，但有速率限制（每分钟1次）

## 速率限制

- **未登录用户**: 每分钟最多1次调用
- **登录用户**: 无速率限制，仅消耗积分

## 模型定价信息

以下是各模型的输入token定价（每1000 token）：

| 模型 | 价格（USD/1K tokens） |
|------|---------------------|
| GPT-4 | $0.03 |
| GPT-4 Turbo | $0.01 |
| GPT-3.5 Turbo | $0.0015 |
| GPT-3.5 Turbo 16K | $0.003 |
| Claude 3 | $0.015 |
| Claude 3 Opus | $0.015 |
| Claude 3 Sonnet | $0.003 |
| Claude 3 Haiku | $0.00025 |

## 实际应用场景

1. **API成本预估**: 在调用AI API之前预估成本
2. **内容长度优化**: 优化prompt长度以降低成本
3. **模型选择**: 根据token数量选择合适的模型
4. **批量处理预算**: 批量处理文本时的成本计算

## 注意事项

1. token计算基于tiktoken库，与OpenAI官方计算方式一致
2. Claude模型使用类似GPT-4的编码方式进行估算
3. 成本估算基于当前市场价格，实际价格可能有所变动
4. 不同语言的token/字符比率不同，中文通常比英文比率更高

## 前端集成

该API已集成到前端工具页面中，可通过以下URL访问：

```
http://localhost:3000/tools/token-count
```

前端页面提供了：
- 文本输入框
- 模型选择下拉菜单
- 实时token计数结果
- 成本估算显示
- 多语言支持 