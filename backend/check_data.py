import pymysql
from app.core.config import settings

def check_data():
    # 解析数据库连接信息
    db_url = settings.DATABASE_URL
    parts = db_url.replace('mysql+pymysql://', '').split('@')
    user_pass = parts[0].split(':')
    host_port_db = parts[1].split('/')
    host_port = host_port_db[0].split(':')
    
    connection = pymysql.connect(
        host=host_port[0],
        port=int(host_port[1]),
        user=user_pass[0],
        password=user_pass[1],
        database=host_port_db[1],
        charset='utf8mb4'
    )
    
    try:
        with connection.cursor() as cursor:
            # 检查工具分类
            print("=== 工具分类 ===")
            cursor.execute("SELECT name, icon, description FROM api_tool_categories WHERE is_active = 1")
            categories = cursor.fetchall()
            for cat in categories:
                print(f"分类: {cat[0]}, 图标: {cat[1]}, 描述: {cat[2]}")
            
            print("\n=== API工具 ===")
            cursor.execute("SELECT name, slug, category, is_active FROM api_tools")
            tools = cursor.fetchall()
            for tool in tools:
                print(f"工具: {tool[0]}, slug: {tool[1]}, 分类: {tool[2]}, 活跃: {tool[3]}")
    
    finally:
        connection.close()

if __name__ == "__main__":
    check_data() 