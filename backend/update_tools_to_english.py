#!/usr/bin/env python3
"""
数据库迁移脚本：将工具分类和工具信息从中文更新为英文
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session
from app.core.database import get_db, engine
from app.models.api_tool import APITool, APIToolCategory

def update_categories(db: Session):
    """更新工具分类为英文"""
    category_updates = [
        ("文本处理", {
            "name": "Text Processing",
            "description": "Tools for text processing and transformation"
        }),
        ("文本分析", {
            "name": "Text Analysis",
            "description": "Tools for text content analysis and detection"
        }),
        ("图像生成", {
            "name": "Image Generation",
            "description": "AI tools for generating images from text"
        }),
        ("数据处理", {
            "name": "Data Processing",
            "description": "Tools for data formatting and parsing"
        })
    ]
    
    for old_name, updates in category_updates:
        category = db.query(APIToolCategory).filter(APIToolCategory.name == old_name).first()
        if category:
            print(f"Updating category '{old_name}' to '{updates['name']}'")
            for key, value in updates.items():
                setattr(category, key, value)
        else:
            print(f"Category '{old_name}' not found")
    
    db.commit()
    print("Categories updated successfully!")

def update_tools(db: Session):
    """更新工具信息为英文"""
    tool_updates = [
        ("文本美化", {
            "name": "Text Beautifier",
            "description": "Enhance and improve text expression using AI technology with multiple style transformations",
            "category": "Text Processing",
            "parameters": {
                "text": {
                    "type": "string",
                    "required": True,
                    "description": "Text to be beautified"
                },
                "style": {
                    "type": "string",
                    "default": "formal",
                    "required": False,
                    "description": "Beautification style: formal, casual, creative"
                }
            },
            "example_response": {
                "beautified_text": "This product has excellent practicality and outstanding user experience"
            },
            "example_request": {
                "text": "This product is really good",
                "style": "formal"
            }
        }),
        ("语言检测", {
            "name": "Language Detection",
            "description": "Automatically detect the language type of text with support for multiple languages",
            "category": "Text Analysis"
        }),
        ("文本生成图片", {
            "name": "Text to Image",
            "description": "Generate high-quality images from text descriptions with support for multiple artistic styles",
            "category": "Image Generation"
        }),
        ("JSON解析", {
            "name": "JSON Parser",
            "description": "Parse and beautify JSON format text with formatting and error checking features",
            "category": "Data Processing",
            "parameters": {
                "text": {
                    "type": "string",
                    "required": True,
                    "description": "JSON text to be parsed"
                },
                "format_type": {
                    "type": "string",
                    "default": "pretty",
                    "required": False,
                    "description": "Format type: pretty, compact, validate"
                },
                "indent": {
                    "type": "integer",
                    "minimum": 2,
                    "maximum": 8,
                    "default": 4,
                    "description": "Number of indent spaces"
                }
            },
            "example_request": {
                "text": '{"name":"John","age":25,"city":"New York"}',
                "format_type": "pretty",
                "indent": 4
            }
        })
    ]
    
    for old_name, updates in tool_updates:
        tool = db.query(APITool).filter(APITool.name == old_name).first()
        if tool:
            print(f"Updating tool '{old_name}' to '{updates['name']}'")
            for key, value in updates.items():
                if hasattr(tool, key):
                    setattr(tool, key, value)
                else:
                    print(f"Warning: Field '{key}' not found in APITool model")
        else:
            print(f"Tool '{old_name}' not found")
    
    db.commit()
    print("Tools updated successfully!")

def main():
    """执行数据迁移"""
    print("Starting database migration: updating tools to English...")
    
    # 获取数据库会话
    db = next(get_db())
    
    try:
        # 更新分类
        update_categories(db)
        
        # 更新工具
        update_tools(db)
        
        print("Migration completed successfully!")
        
    except Exception as e:
        print(f"Migration failed: {e}")
        db.rollback()
        raise
        
    finally:
        db.close()

if __name__ == "__main__":
    main() 