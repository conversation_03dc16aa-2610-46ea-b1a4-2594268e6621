-- AI工具API平台 MySQL数据库结构
-- 创建数据库
CREATE DATABASE IF NOT EXISTS aitools CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE aitools;

-- 用户表
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    hashed_password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    is_superuser BOOLEAN DEFAULT FALSE,
    credits INT DEFAULT 100,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- API工具表
CREATE TABLE api_tools (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    category VARCHAR(50) NOT NULL,
    endpoint VARCHAR(200) NOT NULL,
    method VARCHAR(10) DEFAULT 'POST',
    cost_per_call INT DEFAULT 1,
    is_active BOOLEAN DEFAULT TRUE,
    parameters JSON,
    example_request JSON,
    example_response JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_slug (slug),
    INDEX idx_category (category),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- API使用记录表
CREATE TABLE api_usage (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    tool_id INT NOT NULL,
    request_data JSON,
    response_data JSON,
    status VARCHAR(20) DEFAULT 'success',
    error_message TEXT,
    cost INT DEFAULT 1,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (tool_id) REFERENCES api_tools(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_tool_id (tool_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_ip_address (ip_address)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 积分交易记录表
CREATE TABLE credit_transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    amount INT NOT NULL,
    transaction_type ENUM('purchase', 'usage', 'refund', 'bonus') NOT NULL,
    description VARCHAR(255),
    reference_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_transaction_type (transaction_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 速率限制表（用于未认证用户的限流）
CREATE TABLE rate_limits (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ip_address VARCHAR(45) NOT NULL,
    tool_slug VARCHAR(100) NOT NULL,
    last_call_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    call_count INT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_ip_tool (ip_address, tool_slug),
    INDEX idx_ip_address (ip_address),
    INDEX idx_tool_slug (tool_slug),
    INDEX idx_last_call_time (last_call_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入初始API工具数据
INSERT INTO api_tools (name, slug, description, category, endpoint, cost_per_call, parameters, example_request, example_response) VALUES
(
    '文本美化',
    'text-beautify',
    '使用AI技术美化和改进文本表达，支持多种风格转换',
    'text',
    '/api/v1/ai-tools/text-beautify',
    2,
    JSON_OBJECT(
        'text', JSON_OBJECT('type', 'string', 'required', true, 'description', '需要美化的文本'),
        'style', JSON_OBJECT('type', 'string', 'required', false, 'description', '美化风格：formal, casual, creative', 'default', 'formal')
    ),
    JSON_OBJECT('text', '这个产品很好用', 'style', 'formal'),
    JSON_OBJECT('beautified_text', '这款产品具有卓越的实用性和优异的用户体验')
),
(
    '语言检测',
    'language-detect',
    '自动检测文本的语言类型，支持多种语言识别',
    'text',
    '/api/v1/ai-tools/language-detect',
    1,
    JSON_OBJECT(
        'text', JSON_OBJECT('type', 'string', 'required', true, 'description', '需要检测语言的文本')
    ),
    JSON_OBJECT('text', 'Hello world, this is a test.'),
    JSON_OBJECT('language', 'en', 'confidence', 0.99, 'language_name', 'English')
),
(
    '文本生成图片',
    'text-to-image',
    '根据文本描述生成高质量图片，支持多种艺术风格',
    'image',
    '/api/v1/ai-tools/text-to-image',
    5,
    JSON_OBJECT(
        'prompt', JSON_OBJECT('type', 'string', 'required', true, 'description', '图片描述文本'),
        'style', JSON_OBJECT('type', 'string', 'required', false, 'description', '图片风格：realistic, anime, artistic', 'default', 'realistic'),
        'size', JSON_OBJECT('type', 'string', 'required', false, 'description', '图片尺寸：512x512, 1024x1024', 'default', '512x512')
    ),
    JSON_OBJECT('prompt', 'A beautiful sunset over the ocean', 'style', 'realistic', 'size', '512x512'),
    JSON_OBJECT('image_url', 'https://example.com/generated-image.jpg', 'width', 512, 'height', 512)
);

-- 创建管理员用户（密码：admin123，需要在应用中重新哈希）
INSERT INTO users (username, email, hashed_password, full_name, is_superuser, credits) VALUES
('admin', '<EMAIL>', '$2b$12$placeholder_hash', '系统管理员', TRUE, 10000); 