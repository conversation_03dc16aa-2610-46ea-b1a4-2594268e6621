import pymysql
from app.core.config import settings

def update_categories():
    # 解析数据库连接信息
    db_url = settings.DATABASE_URL
    parts = db_url.replace('mysql+pymysql://', '').split('@')
    user_pass = parts[0].split(':')
    host_port_db = parts[1].split('/')
    host_port = host_port_db[0].split(':')
    
    connection = pymysql.connect(
        host=host_port[0],
        port=int(host_port[1]),
        user=user_pass[0],
        password=user_pass[1],
        database=host_port_db[1],
        charset='utf8mb4'
    )
    
    try:
        with connection.cursor() as cursor:
            # 更新工具分类映射
            category_mapping = {
                'text': '文本处理',  # 文本美化可以归为文本处理
                'image': '图像生成'   # 文本生成图片归为图像生成
            }
            
            # 更新分类
            for old_category, new_category in category_mapping.items():
                print(f"更新分类 '{old_category}' -> '{new_category}'")
                cursor.execute(
                    "UPDATE api_tools SET category = %s WHERE category = %s",
                    (new_category, old_category)
                )
                affected_rows = cursor.rowcount
                print(f"更新了 {affected_rows} 个工具")
            
            # 将语言检测单独归为文本分析
            print("更新语言检测工具分类为 '文本分析'")
            cursor.execute(
                "UPDATE api_tools SET category = %s WHERE slug = %s",
                ('文本分析', 'language-detect')
            )
            affected_rows = cursor.rowcount
            print(f"更新了 {affected_rows} 个工具")
            
            connection.commit()
            print("分类更新完成！")
            
            # 验证更新结果
            print("\n=== 更新后的工具分类 ===")
            cursor.execute("SELECT name, slug, category FROM api_tools")
            tools = cursor.fetchall()
            for tool in tools:
                print(f"工具: {tool[0]}, slug: {tool[1]}, 分类: {tool[2]}")
                
    except Exception as e:
        print(f"更新失败: {e}")
        connection.rollback()
    finally:
        connection.close()

if __name__ == "__main__":
    update_categories() 